// frontend/src/UsageLogger.js

const getBaseUrl = () => {
    // Retrieve baseUrl, assuming it's stored in localStorage as in App.jsx
    // Or define a default if not found, though it should ideally be consistent.
    return localStorage.getItem('baseUrl') || '/api';
};

/**
 * Logs a general usage event.
 * @param {string} eventType - The type of event (e.g., 'click', 'navigation', 'load').
 * @param {string} component - The component or page where the event occurred.
 * @param {string} message - A descriptive message for the event.
 * @param {object} [details={}] - Optional additional details for the event.
 */
export const logEvent = async (eventType, component, message, details = {}) => {
    const baseUrl = getBaseUrl();
    try {
        await fetch(`${baseUrl}/log-frontend-event`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                event_type: eventType,
                component: component,
                message: message,
                details: details,
            }),
        });
    } catch (error) {
        console.error('Failed to send event log to backend:', error);
        // Optionally, implement a retry mechanism or local fallback
    }
};

/**
 * Logs an error that occurred in the frontend.
 * @param {string} errorMessage - The primary error message.
 * @param {string} component - The component where the error occurred.
 * @param {object} [details={}] - Optional additional details (e.g., error stack, state).
 */
export const logError = async (errorMessage, component, details = {}) => {
    const baseUrl = getBaseUrl();
    try {
        await fetch(`${baseUrl}/log-frontend-error`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                error_message: errorMessage,
                component: component,
                details: details,
            }),
        });
    } catch (error) {
        console.error('Failed to send error log to backend:', error);
        // Optionally, implement a retry mechanism or local fallback
    }
};
