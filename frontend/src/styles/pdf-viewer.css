/* CSS for highlighting text in PDF */
.highlighted-text {
  background-color: rgba(255, 255, 0, 0.4) !important;
  border-radius: 2px;
  padding: 0 1px;
}

/* Styling for search status indicators */
.search-success {
  background-color: rgba(220, 252, 231, 0.5);
  border: 1px solid #86efac;
}

.search-error {
  background-color: rgba(254, 226, 226, 0.5);
  border: 1px solid #fca5a5;
}

.search-progress {
  background-color: rgba(219, 234, 254, 0.5);
  border: 1px solid #93c5fd;
}

/* Animation for search indicator */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.search-spinner {
  animation: spin 1s linear infinite;
}

/* Improve PDF viewer controls */
.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
}

.pdf-controls button {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.pdf-controls button:not(:disabled):hover {
  background-color: #dbeafe;
}

.pdf-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Improve property list item styling */
.property-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.property-item:hover {
  background-color: #eff6ff;
}

.property-item.active {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
}
