import React from 'react';

// PDF Viewer Component with improved rendering
const PdfViewer = React.memo(({ url }) => {
    const [isLoading, setIsLoading] = React.useState(true);
    const iframeRef = React.useRef(null);

    React.useEffect(() => {
        if (iframeRef.current) {
            iframeRef.current.onload = () => setIsLoading(false);
        }
    }, [url]);

    if (!url) {
        return (
            <div className="flex items-center justify-center h-full bg-gray-100 rounded-lg border border-gray-300 min-h-[600px]">
                <p className="text-gray-500">Upload a PDF to view it here</p>
            </div>
        );
    }
    
    return (
        <div className="pdf-container w-full overflow-hidden rounded-lg shadow-md h-full" style={{ position: 'relative' }}>
            <div className="aspect-ratio-container relative h-full" style={{ paddingTop: '141.4%' }}>
                <div style={{ 
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    backgroundColor: '#fff',
                    visibility: isLoading ? 'visible' : 'hidden'
                }}>
                    <div className="flex items-center justify-center h-full">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                </div>
                <iframe
                    ref={iframeRef}
                    src={url}
                    className="absolute top-0 left-0 w-full h-full border-0"
                    style={{
                        visibility: isLoading ? 'hidden' : 'visible',
                        transform: 'translate3d(0,0,0)',
                        backfaceVisibility: 'hidden',
                        WebkitBackfaceVisibility: 'hidden',
                        willChange: 'transform'
                    }}
                    title="PDF Viewer"
                />
            </div>
        </div>
    );
}, (prevProps, nextProps) => prevProps.url === nextProps.url);

export default PdfViewer; 