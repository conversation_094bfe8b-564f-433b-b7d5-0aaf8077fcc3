import React, { useState, useEffect, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import '../styles/pdf-viewer.css';

pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.js';

const EnhancedPdfViewer = ({ url, searchText: externalSearchText, onSearchResult }) => {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [pdfDocument, setPdfDocument] = useState(null);
  const [searchResults, setSearchResults] = useState([]);
  const [currentMatch, setCurrentMatch] = useState(0);
  const [isSearching, setIsSearching] = useState(false);
  const containerRef = useRef(null);
  const pagesRef = useRef([]);
  // New state for internal search
  const [internalSearchText, setInternalSearchText] = useState('');
  const [activeSearchText, setActiveSearchText] = useState(externalSearchText || '');
  const [scale, setScale] = useState(1.0); // Add this state for zoom

  // Initialize refs array when numPages changes
  useEffect(() => {
    if (numPages) {
      pagesRef.current = Array(numPages).fill().map((_, i) => pagesRef.current[i] || React.createRef());
    }
  }, [numPages]);

  // Scroll to current page when pageNumber changes
  useEffect(() => {
    if (pagesRef.current[pageNumber - 1] && pagesRef.current[pageNumber - 1].current) {
      // Use 'nearest' to minimize scrolling and keep the floating panel visible
      pagesRef.current[pageNumber - 1].current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
  }, [pageNumber, numPages]); // Also depend on numPages to re-run if refs are re-initialized

  // Handle document loading
  function onDocumentLoadSuccess(pdf) {
    setNumPages(pdf.numPages);
    setPdfDocument(pdf);
    setIsLoading(false);
  }

  // Update active search text when external search text changes
  useEffect(() => {
    if (externalSearchText !== undefined) {
      setActiveSearchText(externalSearchText);
      setInternalSearchText(externalSearchText);
    }
  }, [externalSearchText]);

  // Search for text in PDF when activeSearchText changes
  useEffect(() => {
    if (pdfDocument) {
      searchInPdf(activeSearchText);
    }
  }, [activeSearchText, pdfDocument]);

  // Handle internal search submission
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    setActiveSearchText(internalSearchText);
  };

  // Custom text renderer for highlighting
  const renderCustomTextLayer = (textItem) => {
    if (!activeSearchText || activeSearchText.length === 0) {
      return textItem.str.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    }

    const itemText = textItem.str;
    const itemTextLower = itemText.toLowerCase();
    const escapedText = itemText.replace(/</g, '&lt;').replace(/>/g, '&gt;');
    const searchTextLower = activeSearchText.toLowerCase();

    let isMatch = false;

    if (isDateLike(activeSearchText)) {
      // For date searches, use comprehensive date format matching but be more precise
      const searchTerms = generateDateFormats(activeSearchText);
      const itemTerm = itemTextLower.trim();

      // For date matching, be more strict about what constitutes a match
      isMatch = searchTerms.some(term => {
        // Exact match
        if (itemTerm === term) return true;

        // For longer terms (full dates), allow contains matching
        if (term.length >= 8 && itemTerm.includes(term)) return true;

        // For shorter terms, only allow if they're not just numbers (to avoid matching standalone years)
        if (term.length >= 4 && term.length < 8 && !/^\d+$/.test(term) && itemTerm.includes(term)) return true;

        // Allow partial matching only for very specific cases (like month names)
        if (term.includes(itemTerm) && itemTerm.length > term.length/3 && !/^\d+$/.test(itemTerm)) return true;

        return false;
      });
    } else if (isNumericLike(activeSearchText)) {
      // For numeric searches, require exact match after normalization
      const normalizedSearch = normalizeNumeric(activeSearchText);
      const normalizedItem = normalizeNumeric(itemText);

      // Exact match or the item text contains the search number
      isMatch = normalizedItem === normalizedSearch ||
                (normalizedItem.includes(normalizedSearch) && normalizedSearch.length >= 2);
    } else {
      // For string searches, use improved matching
      const searchTerm = searchTextLower.trim();
      const itemTerm = itemTextLower.trim();

      if (searchTerm.length <= 2) {
        // For very short search terms, require exact match
        isMatch = itemTerm === searchTerm;
      } else if (searchTerm.length <= 4) {
        // For short search terms, require exact match or starts with
        isMatch = itemTerm === searchTerm || itemTerm.startsWith(searchTerm);
      } else {
        // For longer search terms, allow fuzzy matching
        isMatch = itemTerm.includes(searchTerm) ||
                  calculateStringSimilarity(itemTerm, searchTerm) >= 0.8;
      }
    }

    if (isMatch) {
      return `<span style="background-color: rgba(255, 255, 0, 0.4); border-radius: 2px; padding: 0 1px;">${escapedText}</span>`;
    }

    return escapedText;
  };

  // Helper function to detect if text looks like a date
  const isDateLike = (text) => {
    // Basic patterns that suggest a date
    const datePatterns = [
      /^\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}$/,  // MM/DD/YYYY, MM-DD-YYYY, MM.DD.YYYY
      /^\d{2,4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2}$/,  // YYYY/MM/DD, YYYY-MM-DD
      /^\d{1,2}\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i, // DD MMM
      /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\s+\d{1,2}/i, // MMM DD
      /^\d{4}$/  // Just a year
    ];

    return datePatterns.some(pattern => pattern.test(text.trim()));
    // const cleanText = text.trim();
    // const parsedDate = parseDate(cleanText);
    // return !isNaN(parsedDate);
  };

  const parseDate = (cleanText) => {
  try {
    let parsedDate;

    // Handle various input formats
    if (/^\d{4}$/.test(cleanText)) {
      // Just a year - create a date for January 1st
      parsedDate = new Date(parseInt(cleanText), 0, 1);
    } else {
      // Try standard Date parsing first
      parsedDate = new Date(cleanText);

      // If that fails, try some common formats
      if (isNaN(parsedDate)) {
        // Try DD/MM/YYYY format (swap day/month)
        const parts = cleanText.split(/[\/\-\.]/);
        if (parts.length === 3) {
          // Try MM/DD/YYYY
          parsedDate = new Date(parts[2], parts[0] - 1, parts[1]);
          if (isNaN(parsedDate)) {
            // Try DD/MM/YYYY
            parsedDate = new Date(parts[2], parts[1] - 1, parts[0]);
          }
        }
      }
    }
    return parsedDate;
  } catch (error) {
    console.log('Date parsing error:', error);
    return NaN; // Return an invalid date if parsing fails
  }
}

  // Helper function to detect if text looks like a number
  const isNumericLike = (text) => {
    const cleanText = text.trim();
    // Match integers, decimals, numbers with commas, currency symbols, percentages
    const numericPatterns = [
      /^\d+$/,                           // Simple integers: 123
      /^\d+\.\d+$/,                      // Decimals: 123.45
      /^\d{1,3}(,\d{3})*$/,             // Numbers with commas: 1,234 or 1,234,567
      /^\d{1,3}(,\d{3})*\.\d+$/,        // Decimals with commas: 1,234.56
      /^[R$€£¥]\s*\d+(\.\d+)?$/,        // Currency: R123, $123.45
      /^[R$€£¥]\s*\d{1,3}(,\d{3})*(\.\d+)?$/, // Currency with commas: R1,234.56
      /^\d+(\.\d+)?%$/,                 // Percentages: 25% or 25.5%
      /^\d+\s*%$/                       // Percentages with space: 25 %
    ];

    return numericPatterns.some(pattern => pattern.test(cleanText));
  };

  // Helper function to normalize numeric text for comparison
  const normalizeNumeric = (text) => {
    return text.trim()
      .replace(/[R$€£¥\s]/g, '')  // Remove currency symbols and spaces
      .replace(/,/g, '')          // Remove commas
      .replace(/%/g, '');         // Remove percentage signs
  };

  // Helper function for fuzzy string matching using simple similarity
  const calculateStringSimilarity = (str1, str2) => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    // Simple character-based similarity
    const editDistance = levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  };

  // Simple Levenshtein distance implementation
  const levenshteinDistance = (str1, str2) => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };
  
  // Helper function to parse date and generate multiple formats
  const generateDateFormats = (dateText) => {
    const formats = new Set();
    const cleanText = dateText.trim();
    
    // Add the original text
    formats.add(cleanText.toLowerCase());

    const parsedDate = parseDate(cleanText);

    if (!isNaN(parsedDate)) {
      const year = parsedDate.getFullYear();
      const month = parsedDate.getMonth() + 1;
      const day = parsedDate.getDate();
      
      // Pad with zeros and non-padded versions
      const mm = month.toString().padStart(2, '0');
      const dd = day.toString().padStart(2, '0');
      const m = month.toString(); // Non-padded month
      const d = day.toString();   // Non-padded day
      const yyyy = year.toString();
      const yy = year.toString().slice(-2);
      
      // Month names
      const monthNames = [
        'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december'
      ];
      const monthAbbr = [
        'jan', 'feb', 'mar', 'apr', 'may', 'jun',
        'jul', 'aug', 'sep', 'oct', 'nov', 'dec'
      ];
      
      const monthName = monthNames[month - 1];
      const monthShort = monthAbbr[month - 1];
      
      // Generate various formats
      formats.add(`${mm}/${dd}/${yyyy}`);
      formats.add(`${m}/${d}/${yyyy}`);
      formats.add(`${mm}/${dd}/${yy}`);
      formats.add(`${m}/${d}/${yy}`);
      formats.add(`${mm}-${dd}-${yyyy}`);
      formats.add(`${m}-${d}-${yyyy}`);
      formats.add(`${mm}-${dd}-${yy}`);
      formats.add(`${m}-${d}-${yy}`);
      formats.add(`${mm}.${dd}.${yyyy}`);
      formats.add(`${m}.${d}.${yyyy}`);
      formats.add(`${mm}.${dd}.${yy}`);
      formats.add(`${m}.${d}.${yy}`);
      formats.add(`${dd}/${mm}/${yyyy}`);
      formats.add(`${d}/${m}/${yyyy}`);
      formats.add(`${dd}/${mm}/${yy}`);
      formats.add(`${d}/${m}/${yy}`);
      formats.add(`${dd}-${mm}-${yyyy}`);
      formats.add(`${d}-${m}-${yyyy}`);
      formats.add(`${dd}-${mm}-${yy}`);
      formats.add(`${d}-${m}-${yy}`);
      formats.add(`${dd}.${mm}.${yyyy}`);
      formats.add(`${d}.${m}.${yyyy}`);
      formats.add(`${dd}.${mm}.${yy}`);
      formats.add(`${d}.${m}.${yy}`);
      
      // ISO format
      formats.add(`${yyyy}-${mm}-${dd}`);
      formats.add(`${yyyy}/${mm}/${dd}`);
      
      // Written formats
      formats.add(`${monthName} ${d}, ${yyyy}`);
      formats.add(`${monthName} ${dd}, ${yyyy}`);
      formats.add(`${monthShort} ${d}, ${yyyy}`);
      formats.add(`${monthShort} ${dd}, ${yyyy}`);
      formats.add(`${d} ${monthName} ${yyyy}`);
      formats.add(`${dd} ${monthName} ${yyyy}`);
      formats.add(`${d} ${monthShort} ${yyyy}`);
      formats.add(`${dd} ${monthShort} ${yyyy}`);
      
      // Without year
      formats.add(`${monthName} ${d}`);
      formats.add(`${monthName} ${dd}`);
      formats.add(`${monthShort} ${d}`);
      formats.add(`${monthShort} ${dd}`);
      formats.add(`${mm}/${dd}`);
      formats.add(`${m}/${d}`);
      formats.add(`${mm}-${dd}`);
      formats.add(`${m}-${d}`);
      formats.add(`${mm}.${dd}`);
      formats.add(`${m}.${d}`);
      formats.add(`${dd}/${mm}`);
      formats.add(`${d}/${m}`);
      formats.add(`${dd}-${mm}`);
      formats.add(`${d}-${m}`);
      formats.add(`${dd}.${mm}`);
      formats.add(`${d}.${m}`);
      
    }
    
    
    return Array.from(formats);
  };
  // Search in PDF function
  const searchInPdf = async (text) => {
    // If text is empty, clear search results and highlights
    if (!text || text.trim() === "") {
      setSearchResults([]);
      setCurrentMatch(0);
      if (onSearchResult) {
        onSearchResult('cleared');
      }
      return;
    }
  
    if (!pdfDocument) return;
  
    setIsSearching(true);
    setSearchResults([]);
  
    try {
      const results = [];
      const searchTextLower = text.toLowerCase();

      console.log('Searching for:', text); // Debug log
      if (isDateLike(text)) {
          // For date searches, use comprehensive date format matching but be more precise
          const searchTerms = generateDateFormats(text);
          console.log('Generated date formats:', searchTerms); // Debug log
      }

      // Search in each page
      for (let i = 1; i <= numPages; i++) {
        const page = await pdfDocument.getPage(i);
        const textContent = await page.getTextContent();
        const textItems = textContent.items;

        // Search for the text in this page's content
        for (let j = 0; j < textItems.length; j++) {
          const item = textItems[j];
          const itemText = item.str;
          const itemTextLower = itemText.toLowerCase();

          let isMatch = false;
          let matchedTerm = '';

          if (isDateLike(text)) {
            // For date searches, use comprehensive date format matching but be more precise
            const searchTerms = generateDateFormats(text);
            const itemTerm = itemTextLower.trim();
            
            // For date matching, be more strict about what constitutes a match
            const foundTerm = searchTerms.find(term => {
              // Exact match
              if (itemTerm === term) return true;

              if (term.length <= 4) return false; // very short terms must be exact matches

              // For longer terms (full dates), allow contains matching
              if (term.length >= 8 && itemTerm.includes(term)) return true;

              // For shorter terms, only allow if they're not just numbers (to avoid matching standalone years)
              if (term.length >= 4 && term.length < 8 && !/^\d+$/.test(term) && itemTerm.includes(term)) return true;


              // Allow partial matching only for very specific cases (like month names)
              if (term.includes(itemTerm) && itemTerm.length > term.length/3 && !/^\d+$/.test(itemTerm)) return true;

              return false;
            });

            if (foundTerm) {
              isMatch = true;
              matchedTerm = foundTerm;
            }
          } else if (isNumericLike(text)) {
            // For numeric searches, require exact match after normalization
            const normalizedSearch = normalizeNumeric(text);
            const normalizedItem = normalizeNumeric(itemText);

            // Exact match or the item text contains the search number
            if (normalizedItem === normalizedSearch ||
                (normalizedItem.includes(normalizedSearch) && normalizedSearch.length >= 2)) {
              isMatch = true;
              matchedTerm = normalizedSearch;
            }
          } else {
            // For string searches, use improved matching
            const searchTerm = searchTextLower.trim();
            const itemTerm = itemTextLower.trim();

            if (searchTerm.length <= 2) {
              // For very short search terms, require exact match
              if (itemTerm === searchTerm) {
                isMatch = true;
                matchedTerm = searchTerm;
              }
            } else if (searchTerm.length <= 4) {
              // For short search terms, require exact match or starts with
              if (itemTerm === searchTerm || itemTerm.startsWith(searchTerm)) {
                isMatch = true;
                matchedTerm = searchTerm;
              }
            } else {
              // For longer search terms, allow fuzzy matching
              if (itemTerm.includes(searchTerm) ||
                  calculateStringSimilarity(itemTerm, searchTerm) >= 0.8) {
                isMatch = true;
                matchedTerm = searchTerm;
              }
            }
          }

          if (isMatch) {
            results.push({
              pageNumber: i,
              itemIndex: j,
              text: item.str,
              transform: item.transform,
              matchedTerm: matchedTerm // Store which term matched for debugging
            });
          }
        }
      }
    
      setSearchResults(results);
    
      if (results.length > 0) {
        // Jump to the first result
        const firstResultPage = results[0].pageNumber;
        setPageNumber(firstResultPage);
        setCurrentMatch(0);
        if (pagesRef.current[firstResultPage - 1] && pagesRef.current[firstResultPage - 1].current) {
          pagesRef.current[firstResultPage - 1].current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      
        // Notify parent component about search success
        if (onSearchResult) {
          onSearchResult('success');
        }
      
        // Highlighting is handled by customTextRenderer
      } else {
        // Notify parent component that no results were found
        if (onSearchResult) {
          onSearchResult('not-found');
        }
      }
    } catch (error) {
      console.error('Error searching PDF:', error);
      if (onSearchResult) {
        onSearchResult('error');
      }
    } finally {
      setIsSearching(false);
    }
  };

  // Navigate to next match
  const goToNextMatch = () => {
    if (searchResults.length === 0) return;
    
    const nextMatch = (currentMatch + 1) % searchResults.length;
    setCurrentMatch(nextMatch);
    const newPage = searchResults[nextMatch].pageNumber;
    setPageNumber(newPage);

    if (pagesRef.current[newPage - 1] && pagesRef.current[newPage - 1].current) {
      pagesRef.current[newPage - 1].current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    // Highlighting is now handled by customTextRenderer.
    // No direct call to highlightSearchResult needed.

  };

  // Navigate to previous match
  const goToPrevMatch = () => {
    if (searchResults.length === 0) return;
    
    const prevMatch = (currentMatch - 1 + searchResults.length) % searchResults.length;
    setCurrentMatch(prevMatch);
    const newPage = searchResults[prevMatch].pageNumber;
    setPageNumber(newPage);

    if (pagesRef.current[newPage - 1] && pagesRef.current[newPage - 1].current) {
      pagesRef.current[newPage - 1].current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
    
    // Highlighting is now handled by customTextRenderer.
    // No direct call to highlightSearchResult needed.
  }
  // Page navigation
  const changePage = (offset) => {
    setPageNumber(prevPageNumber => {
      const newPageNumber = prevPageNumber + offset;
      // Scrolling will be handled by the useEffect hook watching pageNumber
      return Math.min(Math.max(1, newPageNumber), numPages);
    });
  };

  const handleZoomIn = () => setScale(prev => Math.min(prev + 0.2, 3));
  const handleZoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.5));
  const handleResetZoom = () => setScale(1.0);

  return (
    <>
      <div className="pdf-container w-full rounded-lg shadow-md flex flex-col relative">
        {/* Fixed Page Navigation Bar */}
        <div className="bg-gray-100 p-2 flex flex-col sm:flex-row justify-between items-center border-b gap-2">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => changePage(-1)}
              disabled={pageNumber <= 1}
              className={`px-2 py-1 rounded ${pageNumber <= 1 ? 'text-gray-400' : 'text-blue-600 hover:bg-blue-100'}`}
            >
              Previous
            </button>
            <span className="text-sm">
              Page {pageNumber} of {numPages || '--'}
            </span>
            <button
              onClick={() => changePage(1)}
              disabled={pageNumber >= numPages}
              className={`px-2 py-1 rounded ${pageNumber >= numPages ? 'text-gray-400' : 'text-blue-600 hover:bg-blue-100'}`}
            >
              Next
            </button>
          </div>
          {/* Zoom Controls */}
          <div className="flex items-center space-x-2">
            <button onClick={handleZoomOut} className="px-2 py-1 rounded text-blue-600 hover:bg-blue-100" title="Zoom Out">-</button>
            <span className="text-sm">{Math.round(scale * 100)}%</span>
            <button onClick={handleZoomIn} className="px-2 py-1 rounded text-blue-600 hover:bg-blue-100" title="Zoom In">+</button>
            <button onClick={handleResetZoom} className="px-2 py-1 rounded text-gray-600 hover:bg-gray-200" title="Reset Zoom">Reset</button>
          </div>
          {/* Basic Search Bar in Header */}
          <form onSubmit={handleSearchSubmit} className="flex items-center space-x-2">
            <input
              type="text"
              value={internalSearchText}
              onChange={(e) => setInternalSearchText(e.target.value)}
              placeholder="Search in PDF..."
              className="px-3 py-1 border rounded text-sm"
            />
            <button
              type="submit"
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              Search
            </button>
          </form>
        </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Searching indicator */}
      {isSearching && (
        <div className="absolute top-0 left-0 w-full h-full bg-white bg-opacity-70 flex items-center justify-center z-30">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2 text-blue-600">Searching...</p>
          </div>
        </div>
      )}

      {/* PDF Document */}
      <div
        ref={containerRef}
        className="flex-1 min-h-[400px] max-h-[700px] overflow-auto relative z-10"
        style={{ height: '700px' }} // You can adjust this value as needed
      >
        <Document
          file={url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={(error) => console.error('Error loading PDF:', error)}
          className="flex flex-col items-center"
        >
          <div ref={pagesRef.current[pageNumber - 1]}>
            <Page
              key={`page_${pageNumber}`}
              pageNumber={pageNumber}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              customTextRenderer={renderCustomTextLayer}
              className="mb-4"
              scale={scale} // Pass the scale prop here
            />
          </div>
        </Document>
      </div>
      
      {/* 
        The .highlighted-text class in styled-jsx might not be accessible
        to the strings returned by customTextRenderer as they are parsed by react-pdf.
        Inline styles are used in renderCustomTextLayer for this reason.
        If needed, this <style jsx> block can be removed or adjusted if other global styles depend on it.
        For now, let's keep it, as it might be used by other parts or if customTextRenderer could access it.
      */}
      <style>{`
        .highlighted-text {
          background-color: rgba(255, 255, 0, 0.4) !important;
          border-radius: 2px;
          padding: 0 1px;
        }
      `}</style>
      </div>

      {/* Floating Search Results Panel - Completely outside PDF container */}
      {searchResults.length > 0 && (
        <div
          className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white border border-gray-300 rounded-lg shadow-lg p-3 min-w-64"
          style={{
            zIndex: 9999,
            position: 'fixed',
            bottom: '1rem',
            left: '50%',
            transform: 'translateX(-50%)'
          }}
        >
          {/* Search Results Navigation */}
          <div className="flex items-center justify-between space-x-3">
            <button
              onClick={goToPrevMatch}
              className="px-3 py-2 text-blue-600 hover:bg-blue-100 rounded font-medium"
              title="Previous match"
            >
              ← Previous
            </button>
            <span className="text-sm text-center font-medium px-2">
              {currentMatch + 1} of {searchResults.length} matches
            </span>
            <button
              onClick={goToNextMatch}
              className="px-3 py-2 text-blue-600 hover:bg-blue-100 rounded font-medium"
              title="Next match"
            >
              Next →
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default EnhancedPdfViewer;


