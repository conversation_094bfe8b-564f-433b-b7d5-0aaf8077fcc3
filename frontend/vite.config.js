import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 400,
    proxy: {
      '/api': {
        target: 'http://backend:6060',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, ''),
      }
    },
    allowedHosts: ['tdm_contract_processor.sapconet.org', 'tourvest.darcartz.com', 'localhost'],
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
  },
  css: {
    postcss: './postcss.config.js'
  }
}) 