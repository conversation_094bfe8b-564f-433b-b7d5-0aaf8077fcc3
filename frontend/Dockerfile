# Stage 1: Builder
FROM node:23-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Copy the pdf worker
RUN cp node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.worker.mjs public/pdf.worker.js

# Build the application for production
RUN npm run build

# Stage 2: Development
FROM node:23-alpine AS development

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Copy the pdf worker
RUN cp node_modules/react-pdf/node_modules/pdfjs-dist/build/pdf.worker.mjs public/pdf.worker.js

# Expose Vite's default port
EXPOSE 400

# Start the development server
CMD ["npm", "run", "dev", "--", "--host"]

# Stage 3: Production
FROM nginx:alpine AS production

# Copy built static files from the builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy the Nginx configuration file
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 400

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
