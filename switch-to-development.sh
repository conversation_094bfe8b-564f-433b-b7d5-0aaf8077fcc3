#!/bin/bash

# Switch to Development Environment

echo "Switching to Development environment..."

# Copy development environment file
cp .env.development .env

echo "✅ Switched to Development mode"
echo ""
echo "Configuration:"
echo "- FLASK_ENV=development"
echo "- FRONTEND_TARGET=development"
echo "- Backend: Flask dev server with hot reload, host directory mounting"
echo "- Frontend: Vite dev server with hot reload"
echo ""
echo "To start the application, run:"
echo "  docker compose -f docker-compose.yml -f docker-compose.development.yml up --build"
echo ""
echo "To switch back to production, run:"
echo "  ./switch-to-production.sh"
