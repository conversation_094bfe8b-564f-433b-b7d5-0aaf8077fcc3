# service_doc_processor_TDM
Tourvest Destination Management service doc processing POC



---

## Installation:

- clone the repo
- In backend, create a .env file with the following contents:

        OPENAI_API_KEY=your_openai_key_here

        GEMINI_API_KEY=your_gemini_key_here

        GEMINI_BACKUP_FREE_API_KEY=your_gemini_backup_free_key_here

        ANTHROPIC_API_KEY=your_anthropic_key_here    #optional

        DOC_INTELLIGENCE_AZURE_ENDPOINT=your_azure_endpoint_here

        DOC_INTELLIGENCE_AZURE_KEY=your_azure_key_here

- If not using docker:
    - In the backend folder, create a virtual environment: python -m venv .venv
    - Activate the virtual environment: ".venv\Scripts\activate" if windows cmd, or "source .venv/bin/activate" if linux bash
    - pip install -r requirements.txt

- If using docker:
    - In the main folder, run the following command:

            docker-compose up --build

    - This will build and run the project, and the backend api will be accessible at localhost:6060, and the frontend at localhost:400.

---

## Files

Backend
- Document parsing and data extraction:
        - General_document_parser searches for document-level information
        - property_parser searches for property-level information in the document
        - room_type_parser searches for information on a specific room type
- ProcessorAPI.py exposes some of the above as a flask API. Simply run with python
- New_output.py contains the main function for processing a document and writing the output to a csv, fully automatically.
- Under "evaluations" there are the following files:
        - csv_property_splitter for post-processing - splitting the generated csvs by property
        - DPC_Converter for converting DPC data files into markdown
        - search_eval for evaluating the generated output based on searching against a ground truth document ( eg DPC data)
        - testing.py for miscellaneous testing.

Frontend
- this contains react code to build the frontend to upload pdfs. Can run by running the following command in the frontend folder:

        python -m http.server 400

OR can run both api and frontend using docker - docker needs to be installed and running, and then in the main folder,

        docker-compose up --build

will build and run the project, and

        docker-compose down

will stop the running containers.

When running the program with docker, the backend api will be accessible at localhost:6060, and the frontend at localhost:400.


Microservices require rabbit running:
docker run -it --rm --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:4.0-management

## Logging & Metrics

The application incorporates logging and metrics to provide insights into its usage and operational health.

### Backend Logging
- The backend Flask application (`ProcessorAPI.py`) uses Python's standard `logging` module.
- Logs are output to `stdout`. In a production environment, these logs can be collected by a log aggregation service.
- Key events, such as document processing initiation, success, failure, and API errors, are logged.
- Frontend interactions and errors (sent via `/log-frontend-event` and `/log-frontend-error`) are also logged by the backend.

### Backend Metrics
- The backend exposes a Prometheus-compatible metrics endpoint at `/metrics`.
- This endpoint provides metrics such as:
  - `processed_documents_total`: Total number of documents processed (with labels for document type and status).
  - `file_uploads_total`: Total number of files uploaded (with labels for file type).
  - `auto_process_tasks_total`: Total number of auto-processing tasks initiated.
  - `document_processing_errors_total`: Total count of errors encountered during document processing.
  - Standard Flask request metrics (e.g., request latency, counts by endpoint) provided by `prometheus-flask-exporter`.
- These metrics can be scraped by a Prometheus server for monitoring and alerting.

### Frontend Logging & Metrics
- The frontend application (`App.jsx`) sends usage events and error information to the backend.
- **Usage Events**: Key user interactions (e.g., file selection, analysis submission, navigation) are logged via the `/log-frontend-event` backend endpoint.
- **Error Logs**: Frontend JavaScript errors and issues encountered during API interactions are logged via the `/log-frontend-error` backend endpoint.
- This data is then logged by the backend, allowing for centralized tracking of frontend activity and issues.

## Monitoring & Metrics

The application includes comprehensive monitoring capabilities with Prometheus metrics and visualization options.

### Quick Start Monitoring

1. **Start the full monitoring stack:**
   ```bash
   ./monitoring/start-monitoring.sh
   ```

2. **Access monitoring interfaces:**
   - **Grafana Dashboard**: http://localhost:3000 (admin/admin)
   - **Prometheus**: http://localhost:9090
   - **Simple Metrics Viewer**: Open `monitoring/simple-metrics-viewer.html` in your browser

### Available Metrics

- `processed_documents_total` - Documents processed with success/failure status
- `file_uploads_total` - File uploads by type
- `auto_process_tasks_total` - Auto-processing tasks initiated
- `document_processing_errors_total` - Processing errors
- Standard Flask HTTP metrics (request duration, counts, etc.)

### Monitoring Options

1. **Prometheus + Grafana** (Recommended)
   - Professional monitoring with rich dashboards
   - Real-time alerts and historical data
   - Pre-configured dashboard for document processor metrics

2. **Simple HTML Viewer**
   - Lightweight, standalone metrics viewer
   - No additional services required
   - Perfect for development and quick checks

For detailed monitoring setup and configuration, see [monitoring/README.md](monitoring/README.md).