services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "6060:6060"
    volumes:
      - ${BACKEND_VOLUME:-backend_temp}:/app/temp
      - ${BACKEND_VOLUME_CSVS:-backend_temp_csvs}:/app/temp_csvs
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - FLASK_APP=${FLASK_APP:-ProcessorAPI.py}
      - FLASK_DEBUG=${FLASK_DEBUG:-0}
      - WORKERS=${WORKERS:-4}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6060/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${FRONTEND_TARGET:-production}
    ports:
      - "400:400"
    depends_on:
      - backend
    networks:
      - app-network

  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #     - '--storage.tsdb.retention.time=200h'
  #     - '--web.enable-lifecycle'
  #   networks:
  #     - app-network

  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: grafana
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
  #     - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
  #   environment:
  #     - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
  #     - GF_USERS_ALLOW_SIGN_UP=false
  #   depends_on:
  #     - prometheus
  #   networks:
  #     - app-network

networks:
  app-network:
    driver: bridge

volumes:
  backend_temp:
  backend_temp_csvs:
  backend_temp_dev:
  backend_temp_csvs_dev:
  prometheus_data:
  grafana_data: