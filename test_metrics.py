#!/usr/bin/env python3
"""
Simple script to test the metrics endpoint and verify it's working correctly.
"""

import requests
import time

def test_metrics_endpoint():
    """Test that the metrics endpoint is accessible and returns data."""
    try:
        response = requests.get('http://localhost:6060/metrics', timeout=10)
        
        if response.status_code == 200:
            print("✅ Metrics endpoint is accessible")
            
            # Check for our custom metrics
            metrics_text = response.text
            custom_metrics = [
                'processed_documents_total',
                'file_uploads_total', 
                'auto_process_tasks_total',
                'document_processing_errors_total'
            ]
            
            print("\n📊 Custom Metrics Status:")
            for metric in custom_metrics:
                if metric in metrics_text:
                    print(f"  ✅ {metric} - Found")
                else:
                    print(f"  ❌ {metric} - Missing")
            
            # Check for Flask metrics
            flask_metrics = [
                'flask_http_request_duration_seconds',
                'flask_http_request_total'
            ]
            
            print("\n🌐 Flask Metrics Status:")
            for metric in flask_metrics:
                if metric in metrics_text:
                    print(f"  ✅ {metric} - Found")
                else:
                    print(f"  ❌ {metric} - Missing")
            
            print(f"\n📈 Total metrics response size: {len(metrics_text)} characters")
            
            # Show a sample of the metrics
            print("\n📋 Sample metrics (first 500 characters):")
            print("-" * 50)
            print(metrics_text[:500])
            if len(metrics_text) > 500:
                print("...")
            print("-" * 50)
            
        else:
            print(f"❌ Metrics endpoint returned status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to backend. Make sure it's running on http://localhost:6060")
    except requests.exceptions.Timeout:
        print("❌ Request timed out. Backend might be slow to respond.")
    except Exception as e:
        print(f"❌ Error testing metrics endpoint: {e}")

def test_health_endpoint():
    """Test the health endpoint to make sure the backend is running."""
    try:
        response = requests.get('http://localhost:6060/health', timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Backend health check passed")
            print(f"   Environment: {health_data.get('environment', 'unknown')}")
            print(f"   Status: {health_data.get('status', 'unknown')}")
        else:
            print(f"❌ Health check failed with status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to backend for health check")
    except Exception as e:
        print(f"❌ Error during health check: {e}")

if __name__ == "__main__":
    print("🔍 Testing Document Processor Metrics")
    print("=" * 50)
    
    print("\n1. Testing backend health...")
    test_health_endpoint()
    
    print("\n2. Testing metrics endpoint...")
    test_metrics_endpoint()
    
    print("\n✨ Test completed!")
    print("\n💡 Tips:")
    print("   • If metrics are missing, check the backend logs")
    print("   • Make sure prometheus-flask-exporter is properly installed")
    print("   • Try accessing http://localhost:6060/metrics in your browser")
    print("   • Use the monitoring dashboard at http://localhost:3000 (if Grafana is running)")
