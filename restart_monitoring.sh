#!/bin/bash

# Script to restart monitoring services with updated configuration

echo "🔄 Restarting monitoring services..."

# Stop monitoring services
echo "Stopping Grafana and Prometheus..."
docker compose stop grafana prometheus

# Remove containers to ensure fresh start
echo "Removing containers..."
docker compose rm -f grafana prometheus

# Start services again
echo "Starting services with updated configuration..."
docker compose up -d prometheus grafana

echo "⏳ Waiting for services to be ready..."

# Wait for Prometheus
echo "Waiting for Prometheus..."
timeout=60
while ! curl -s http://localhost:9090/-/ready > /dev/null 2>&1; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "❌ Prometheus failed to start within 60 seconds"
        exit 1
    fi
done
echo "✅ Prometheus is ready"

# Wait for Grafana
echo "Waiting for Grafana..."
timeout=60
while ! curl -s http://localhost:3000/api/health > /dev/null 2>&1; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "❌ Grafana failed to start within 60 seconds"
        exit 1
    fi
done
echo "✅ Grafana is ready"

echo ""
echo "🎉 Monitoring services restarted successfully!"
echo ""
echo "📊 Next steps:"
echo "   1. Check Prometheus targets: http://localhost:9090/targets"
echo "   2. Access Grafana: http://localhost:3000 (admin/admin)"
echo "   3. Run debug script: python debug_monitoring.py"
echo ""
echo "💡 If you still see 'no data' in Grafana:"
echo "   • Make sure your backend is running and accessible"
echo "   • Generate some metrics by using your application"
echo "   • Check the debug script output for issues"
