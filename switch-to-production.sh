#!/bin/bash

# Switch to Production Environment

echo "Switching to Production environment..."

# Copy production environment file
cp .env.production .env

echo "✅ Switched to Production mode"
echo ""
echo "Configuration:"
echo "- FLASK_ENV=production"
echo "- FRONTEND_TARGET=production"
echo "- Backend: Gunicorn with 4 workers, named Docker volumes"
echo "- Frontend: <PERSON><PERSON><PERSON> serving built static files"
echo ""
echo "To start the application, run:"
echo "  docker compose up --build"
echo ""
echo "To switch back to development, run:"
echo "  ./switch-to-development.sh"
