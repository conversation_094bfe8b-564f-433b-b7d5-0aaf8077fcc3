# Environment Setup Guide

This project supports seamless switching between development and production environments using environment variables and Docker multi-stage builds.

## Quick Start

### Production Mode (Default)
```bash
./switch-to-production.sh
docker compose up --build
```

### Development Mode
```bash
./switch-to-development.sh
docker compose -f docker-compose.yml -f docker-compose.development.yml up --build
```

## Environment Configurations

### Production Environment
- **Backend**: Gunicorn WSGI server with 4 workers
- **Frontend**: Nginx serving pre-built static files
- **Volumes**: Named Docker volumes for data persistence
- **Logging**: INFO level
- **Hot Reload**: Disabled
- **Security**: Enhanced (non-root user, restricted CORS)

### Development Environment
- **Backend**: Flask development server with hot reload
- **Frontend**: Vite development server with hot reload
- **Volumes**: Host directory mounting for live code changes
- **Logging**: DEBUG level
- **Hot Reload**: Enabled for both frontend and backend
- **Security**: Relaxed for development convenience

## Environment Files

| File | Purpose |
|------|---------|
| `.env` | Active environment configuration |
| `.env.production` | Production environment template |
| `.env.development` | Development environment template |

## Environment Variables

### Backend Variables
- `FLASK_ENV`: `production` or `development`
- `FLASK_DEBUG`: `0` (prod) or `1` (dev)
- `WORKERS`: Number of Gunicorn workers (prod only)

### Frontend Variables
- `FRONTEND_TARGET`: Docker build target (`production` or `development`)

### Volume Variables
- `BACKEND_VOLUME`: Temp directory volume
- `BACKEND_VOLUME_CSVS`: CSV temp directory volume
- `BACKEND_CODE_VOLUME`: Code directory volume (dev only)
- `FRONTEND_VOLUME`: Frontend code volume (dev only)

## Manual Environment Switching

If you prefer not to use the scripts:

### Switch to Production
```bash
cp .env.production .env
docker compose up --build
```

### Switch to Development
```bash
cp .env.development .env
docker compose -f docker-compose.yml -f docker-compose.development.yml up --build
```

## Docker Multi-Stage Builds

The Dockerfiles now use multi-stage builds:

### Frontend Dockerfile Stages
1. **builder**: Builds the React application
2. **development**: Runs Vite dev server
3. **production**: Serves built files with Nginx

### Backend Dockerfile
- Single stage with environment-based startup script
- Uses `start.sh` to choose between Flask dev server and Gunicorn

## Volume Management

### Production Volumes
- `backend_temp`: Persistent temp files
- `backend_temp_csvs`: Persistent CSV files

### Development Volumes
- Host directory mounting for live code editing
- Changes reflect immediately without rebuilding

## Health Checks

Both environments include health checks:
- **Endpoint**: `http://localhost:6060/health`
- **Interval**: 30 seconds
- **Timeout**: 10 seconds

## Troubleshooting

### Permission Issues
If you encounter permission issues in development:
```bash
# Fix ownership of temp directories
sudo chown -R $USER:$USER backend/temp backend/temp_csvs
```

### Port Conflicts
- Backend: `http://localhost:6060`
- Frontend: `http://localhost:400`

### Environment Not Switching
1. Ensure you rebuild containers: `docker compose up --build`
2. Check active environment: `cat .env`
3. Clear Docker cache if needed: `docker system prune`

## Best Practices

1. **Always rebuild** when switching environments
2. **Commit environment files** to version control
3. **Don't commit** the active `.env` file
4. **Use scripts** for consistent switching
5. **Test in production mode** before deployment
