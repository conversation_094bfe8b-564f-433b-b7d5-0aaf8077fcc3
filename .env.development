# Development Environment Configuration

# Backend Configuration
FLASK_ENV=development
FLASK_APP=ProcessorAPI.py
FLASK_DEBUG=1
WORKERS=1

# Backend Volume Configuration (mount host directory for development)
BACKEND_VOLUME=./backend/temp
BACKEND_VOLUME_CSVS=./backend/temp_csvs
BACKEND_CODE_VOLUME=./backend:/app

# Frontend Configuration
FRONTEND_TARGET=development
FRONTEND_VOLUME=./frontend:/app

# Docker Compose Project Name (optional)
COMPOSE_PROJECT_NAME=service_doc_processor_tdm_dev
