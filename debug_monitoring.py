#!/usr/bin/env python3
"""
A unified command-line tool for testing and monitoring the document processing service.

This script combines the functionality of multiple test scripts into a single,
easy-to-use interface. It can be used to:
  - Check the status of all monitoring components (backend, Prometheus, Grafana).
  - Display current metric values in a human-readable format.
  - Generate test traffic to create metrics data.
  - Run specific, incremental tests to verify that metrics are increasing as expected.

Examples:
  # Run a full status check of the monitoring stack
  python unified_monitoring_tool.py status

  # Show the current metrics from the backend
  python unified_monitoring_tool.py show

  # Generate some test traffic by hitting various endpoints
  python unified_monitoring_tool.py generate

  # Test if the file upload metric increases after an upload
  python unified_monitoring_tool.py test-upload
"""

import argparse
import requests
import json
import time
import io
import re
import sys

# --- Configuration ---
# Central place to configure URLs for easy updates.
BASE_URL = "http://localhost:6060"
METRICS_URL = f"{BASE_URL}/metrics"
HEALTH_URL = f"{BASE_URL}/health"
UPLOAD_URL = f"{BASE_URL}/upload-pdf"
PROPERTIES_URL = f"{BASE_URL}/get-property-names"

PROMETHEUS_URL = "http://localhost:9090"
GRAFANA_URL = "http://localhost:3000"

# --- Core Helper Functions ---

def get_metrics_text():
    """
    Fetches the raw text from the /metrics endpoint with error handling.
    """
    try:
        response = requests.get(METRICS_URL, timeout=10)
        response.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"❌ Error fetching metrics from {METRICS_URL}: {e}", file=sys.stderr)
        return None

def get_specific_metric_value(metrics_text, metric_name):
    """
    Extracts the total value of a specific metric from the metrics text.
    Handles metrics with and without labels.
    """
    if not metrics_text:
        return 0
    total = 0
    # This regex is safer than split() as it handles labels gracefully.
    pattern = re.compile(rf"^{metric_name}(?:{{.*}})?\s+([\d.eE+-]+)", re.MULTILINE)
    matches = pattern.findall(metrics_text)
    for value_str in matches:
        try:
            total += float(value_str)
        except ValueError:
            continue # Ignore if conversion fails
    return total

# --- Mode 1: Full Status Check ---

def run_status_check():
    """
    Performs a comprehensive check of the backend, Prometheus, and Grafana.
    This combines logic from debug_monitoring.py and test_metrics.py.
    """
    print("🔧 Running Full Monitoring Stack Status Check")
    print("=" * 50)

    # 1. Check Backend Metrics
    print("🔍 1. Checking backend service and metrics...")
    try:
        health_response = requests.get(HEALTH_URL, timeout=5)
        if health_response.status_code == 200:
            print("  ✅ Backend is healthy (via /health endpoint)")
        else:
            print(f"  ❌ Backend health check failed: {health_response.status_code}")

        metrics_text = get_metrics_text()
        if metrics_text:
            print("  ✅ Backend metrics are accessible (via /metrics endpoint)")
            lines = [line for line in metrics_text.split('\n') if line and not line.startswith('#')]
            print(f"     📊 Found {len(lines)} metric lines")
            # Check for a key custom metric and a key flask metric
            if 'processed_documents_total' in metrics_text:
                print("     ✅ Key metric 'processed_documents_total' is present.")
            else:
                print("     ❌ Key metric 'processed_documents_total' is MISSING.")
            if 'flask_http_request_total' in metrics_text:
                 print("     ✅ Key metric 'flask_http_request_total' is present.")
            else:
                 print("     ❌ Key metric 'flask_http_request_total' is MISSING.")
    except Exception as e:
        print(f"  ❌ Backend check failed: {e}")

    # 2. Check Prometheus
    print("\n🔍 2. Checking Prometheus...")
    try:
        response = requests.get(f'{PROMETHEUS_URL}/-/healthy', timeout=10)
        if response.status_code == 200:
            print("  ✅ Prometheus is healthy")
        else:
            print(f"  ❌ Prometheus health check failed: {response.status_code}")

        response = requests.get(f'{PROMETHEUS_URL}/api/v1/targets', timeout=10)
        if response.status_code == 200:
            targets_data = response.json().get('data', {}).get('activeTargets', [])
            print("  📋 Prometheus targets:")
            for target in targets_data:
                job = target.get('labels', {}).get('job', 'N/A')
                health = target.get('health', 'unknown')
                status_icon = "    ✅" if health == "up" else "    ❌"
                print(f"{status_icon} {job} - Status: {health}")
    except Exception as e:
        print(f"  ❌ Prometheus check failed: {e}")

    # 3. Check Grafana
    print("\n🔍 3. Checking Grafana...")
    try:
        response = requests.get(f'{GRAFANA_URL}/api/health', timeout=10)
        if response.status_code == 200:
            print("  ✅ Grafana is healthy")
        else:
            print(f"  ❌ Grafana health check failed: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Grafana check failed: {e}")

    print("\n✨ Status check complete.")


# --- Mode 2: Show Current Metrics ---

def display_current_metrics():
    """
    Fetches and displays current metric values in a formatted table.
    This is based on show_current_metrics.py.
    """
    print("📊 Displaying Current Metric Values")
    print("=" * 60)
    metrics_text = get_metrics_text()
    if not metrics_text:
        print("Could not retrieve metrics. Is the backend running?")
        return

    # A simple parser for this display function
    metrics = {}
    for line in metrics_text.split('\n'):
        if line and not line.startswith('#'):
            match = re.match(r'^([a-zA-Z_:][a-zA-Z0-9_:]*(?:\{[^}]*\})?) (.+)$', line)
            if match:
                metric_name, value = match.groups()
                metrics[metric_name] = value

    key_metrics = [
        'file_uploads_total',
        'processed_documents_total',
        'auto_process_tasks_total',
        'document_processing_errors_total',
        'flask_http_request_total',
    ]

    print(f"{'Metric Name':<50} {'Value'}")
    print("-" * 60)

    for metric_base_name in key_metrics:
        found = False
        # Find all variations of the metric (with different labels)
        for name, value in sorted(metrics.items()):
            if name.startswith(metric_base_name):
                print(f"{name:<50} {value}")
                found = True
        if not found:
            print(f"{metric_base_name:<50} 0 (Not Found)")
    print("\n✨ Metrics display complete.")


# --- Mode 3: Generate Test Traffic ---

def generate_test_traffic():
    """
    Calls various endpoints to generate a range of metrics.
    Based on test_metrics_generation.py.
    """
    print("🚀 Generating Test Traffic...")
    print("=" * 50)

    # 1. Hit health endpoint
    print("  - Calling /health endpoint...")
    try:
        requests.get(HEALTH_URL, timeout=5)
    except Exception as e:
        print(f"    Warning: Health endpoint call failed: {e}")

    # 2. Upload a test file
    print("  - Uploading a test file to /upload-pdf...")
    try:
        test_content = b"This is a test PDF content for metrics testing."
        files = {'file': ('test_traffic.pdf', io.BytesIO(test_content), 'application/pdf')}
        requests.post(UPLOAD_URL, files=files, timeout=30)
    except Exception as e:
        print(f"    Warning: File upload failed: {e}")

    # 3. Call a non-existent endpoint
    print("  - Calling a non-existent endpoint...")
    try:
        requests.get(f"{BASE_URL}/nonexistent-endpoint", timeout=5)
    except Exception as e:
         print(f"    (Expected) Request to non-existent endpoint failed: {e}")

    # 4. Make a request that should cause a handled error
    print("  - Calling an endpoint with invalid data...")
    try:
        requests.post(PROPERTIES_URL, json={'invalid': 'data'}, timeout=10)
    except Exception as e:
        print(f"    Warning: Error-generating call failed: {e}")

    print("\n⏳ Waiting 5 seconds for metrics to be collected...")
    time.sleep(5)
    print("\n✨ Test traffic generation complete. Check your metrics now.")
    display_current_metrics()


# --- Mode 4: Incremental Metric Test ---

def run_incremental_test():
    """
    Tests if a specific metric (file_uploads_total) increases after an action.
    Combines logic from test_fixed_metrics.py and test_metrics_simple.py.
    """
    print("🔬 Running Incremental Test for File Uploads")
    print("=" * 50)
    metric_to_test = 'file_uploads_total'

    # 1. Get initial value
    print("  - Getting initial metric value...")
    initial_text = get_metrics_text()
    initial_value = get_specific_metric_value(initial_text, metric_to_test)
    print(f"    Initial '{metric_to_test}' count: {initial_value}")

    # 2. Perform action
    print("  - Uploading a test file...")
    try:
        with open("service_doc_processor_TDM/backend/Contracts/&Beyond/andBeyond Activities 2025 - Botswana WH (1).pdf", "rb") as f:
            files = {'file': ('test_incremental.pdf', f, 'application/pdf')}
            response = requests.post(UPLOAD_URL, files=files, timeout=30)
            print(f"    Upload request completed with status: {response.status_code}")
            if response.status_code >= 400:
                print(f"    WARNING: Upload failed, metric may not increase. Response: {response.text}")

    except Exception as e:
        print(f"  ❌ File upload failed: {e}")
        return

    print("  - Waiting 2 seconds for metrics to update...")
    time.sleep(2)

    # 3. Get updated value
    print("  - Getting updated metric value...")
    updated_text = get_metrics_text()
    updated_value = get_specific_metric_value(updated_text, metric_to_test)
    print(f"    Updated '{metric_to_test}' count: {updated_value}")

    # 4. Compare and report
    if updated_value > initial_value:
        print(f"\n  ✅ SUCCESS: '{metric_to_test}' increased from {initial_value} to {updated_value}.")
    else:
        print(f"\n  ❌ FAILURE: '{metric_to_test}' did not increase. Value is still {updated_value}.")
    print("\n✨ Incremental test complete.")


# --- Main Execution ---

def main():
    """
    Main function to parse command-line arguments and run the selected mode.
    """
    parser = argparse.ArgumentParser(
        description="A unified tool for testing and monitoring the document processing service.",
        formatter_class=argparse.RawTextHelpFormatter  # Keeps help text formatting
    )
    # Using subparsers for a cleaner command-line interface, like 'git push', 'git pull'
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    # Set 'status' as the default command
    parser.set_defaults(func=run_incremental_test)
    subparsers.required = False

    # Define the 'status' command
    parser_status = subparsers.add_parser('status', help='Run a full status check of the monitoring stack (backend, Prometheus, Grafana).')
    parser_status.set_defaults(func=run_status_check)

    # Define the 'show' command
    parser_show = subparsers.add_parser('show', help='Show the current metrics from the backend in a readable format.')
    parser_show.set_defaults(func=display_current_metrics)

    # Define the 'generate' command
    parser_generate = subparsers.add_parser('generate', help='Generate test traffic by calling various endpoints to create metric data.')
    parser_generate.set_defaults(func=generate_test_traffic)

    # Define the 'test-upload' command
    parser_test = subparsers.add_parser('test-upload', help='Run an incremental test to verify the file upload metric increases.')
    parser_test.set_defaults(func=run_incremental_test)

    args = parser.parse_args()
    args.func() # Execute the function associated with the chosen command

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\nAn unexpected error occurred: {e}", file=sys.stderr)
        sys.exit(1)

