#from TourvestServiceDocProcessor_combined import document_processor
from helpers.Document_Processing import Doc_Processor
from helpers.LLM_Handler import LLM_Handler
from general_document_parser import general_document_parser
from property_parser import property_parser
from room_type_parser import room_type_parser
import io
import contextlib
import json

service_providers = ["&Beyond","Africa In Focus","African Anthology","African Safari Collective","African Synergy","Aha","Amakhala","Caleo Foundation","De Hoop Collection","Desert & Delta Safaris","Extraordinary Marketing","Kariega","Karongwe Portfolio","Kim Beyers","Kwandwe Private Reserve","Leeu Collection","Legacy Hotels","Lion Roars","Liz McGrath","Marriott Hotels","Minor Hotels","MORE","Natural Selection","Newmark","One & Only Cape Town","Radisson","Rare Earth Retreats","Red Carnation","Sabi Sabi","SANPARKS","Seasons in Africa","Shamwari","Southern Spoor","Southern Sun","Sun International","Unlimited Destinations","Village & Life",]

document_processor = Doc_Processor()

#document_processor("gemini-2.0-flash").processPdf("test_data/Addo_Elephant_1Page.pdf", "Addo_Elephant_1Page")

model = "gemini-2.0-flash"


def return_Document_Chunks(filename, model="gemini-2.0-flash", length_threshold=5000):
    try:
        pdfstr = Doc_Processor().read_pdf_to_string(filename)
        llm_handler = LLM_Handler(model)
        headings, sections = Doc_Processor().chunk_doc_if_needed(pdfstr, llm_handler, length_threshold=length_threshold)
        # Convert dict_values to lists to make them JSON serializable
        if isinstance(headings, dict):
            headings = list(headings.values())
        if isinstance(sections, dict):
            sections = list(sections.values())
        return {'headings': headings, 'sections': sections}
    except Exception as e:
        print(f"Error processing document: {str(e)}")
        raise e

def test_doc(pdfstr):
    
    doc_parser = general_document_parser(pdfstr, model=model)
    result = {}
    is_valid = doc_parser.get_valid()
    print("is_valid", is_valid)
    result["is_valid"] = is_valid
    if is_valid:
        supplier = doc_parser.get_supplier_name(pdfstr, service_providers)
        result["supplier"] = supplier
        print("supplier", supplier)
        has_sto = doc_parser.general_has_sto()
        result["has_sto"] = has_sto
        print("has_sto", has_sto)
        overarching_period = doc_parser.get_overarching_period()
        result["overarching_period"] = overarching_period
        print("overarching_period", overarching_period)
        periods = doc_parser.get_periods()
        result["periods"] = periods
        print("periods", periods)
        properties = doc_parser.get_property_names()
        result["properties"] = properties
        print("properties", properties)
        specials = doc_parser.get_stay_type()
        result["specials"] = specials
        print("specials", specials)
    return result

def test_property(pdfstr, property_name, periods_to_choose_from = [], validity_period=["2000-01-01","2030-01-01"]):
    doc_parser = property_parser(pdfstr, property_name, model=model)
    is_valid = doc_parser.validate_property()
    print("is_valid:", is_valid)
    result = {}
    result["is_valid"] = is_valid
    if is_valid:
        #headings = doc_parser.get_headings()
        #result["headings"] = headings
        #print("headings:", headings)
        meal_types = doc_parser.get_meal_types()
        result["meal_types"] = meal_types
        print("meal_types:", meal_types)
        periods_at_property = doc_parser.get_periods_at_property(periods_to_choose_from)
        result["periods_at_property"] = periods_at_property
        print("periods_at_property:", periods_at_property)
        checked_periods = doc_parser.check_periods(periods_at_property,validity_period)
        print("checked periods:", checked_periods)
        result["checked_periods"] = checked_periods
        room_types = doc_parser.get_room_types()
        result["room_types"] = room_types
        print("room_types:", room_types)
        has_sto = {}
        for period in checked_periods:
            has_sto[period[0]] = doc_parser.has_sto(period[0],period[1],period[2])
        result["has_sto"] = has_sto
        
        levies = doc_parser.get_levy()
        result["levies"] = levies
        print("levies", levies)
        includes = doc_parser.get_includes()
        result["includes"] = includes
        print("includes",includes)
        excludes = doc_parser.get_excludes()
        result["excludes"] = excludes
        print("excludes",excludes)

        child_policy = doc_parser.general_child_policy()
        result["child_policy"] = child_policy
        print("child_policy",child_policy)


        
    return result

#for now, if sto is mentioned, use only the sto values. If not, don't worry about it. -1 is "no sto", 1 is "sto", 0 is "explicitly use the non-sto (published) rate"
def test_room_type(pdfstr, property_name, room_type, periods = [], meal_types = [], sto_val=-1):
    result = {}
    doc_parser = room_type_parser(pdfstr, property_name, room_type, model=model)
    meal_type = doc_parser.get_meal_type(meal_types)
    result["meal_type"] = meal_type
    print("meal_type", meal_type)

    min_night_stay = doc_parser.min_night_stay()
    result["min_night_stay"] = min_night_stay
    print("min_night_stay:",min_night_stay)
    if min_night_stay==-1:
        min_night_stay_complex = doc_parser.min_night_stay_complex()
        result["min_night_stay"] = min_night_stay_complex
        print("min_night_stay_complex:",min_night_stay_complex)
    
    cancellation_policy = doc_parser.cancellation_policy()
    result["cancellation_policy"] = cancellation_policy
    print("cancellation policy:",cancellation_policy)
  
    if periods and len(periods) > 0:
        for period in periods:    
            capacity = doc_parser.get_room_capacity(period, sto_val)
            result["capacity"] = capacity
            print("capacity:", capacity)
            
            single_rate = doc_parser.get_single_room_rate(period, sto_val)
            result[f"single_rate - {period}"] = single_rate
            print("single_rate:", single_rate)
            total_capacity = capacity['TOTAL_CAPACITY']
            if total_capacity >= 2:
                double_room_info = doc_parser.get_double_room_rate(period,sto_val)
                result[f"double_room_info - {period}"] = double_room_info
                print("double_room_info:", double_room_info)
                if total_capacity >= 3:
                    triple_room_info = doc_parser.get_triple_room_rate( period,sto_val)
                    result[f"triple_room_info - {period}"] = triple_room_info
                    print("triple_room_info:", triple_room_info)
                    if total_capacity >= 4:
                        quad_room_info = doc_parser.get_quad_room_rate(period,sto_val)
                        result[f"quad_room_info - {period}"] = quad_room_info
                        print("quad_room_info:", quad_room_info)
    return result

# test_pdf = "C:/Users/<USER>/Desktop/Sapconet/SapGit/service_doc_processor_TDM/backend/test_data/Addo_Elephant_1Page.pdf"
# test_prop = "ADDO REST CAMP"
# test_seasons = ['Shoulder Season', 'High Season']
# test_validity_period = ['01/11/2023', '31/10/2024']
# test_room = "TENT SITE CK4T"

# test_pdf = "C:/Users/<USER>/Desktop/Sapconet/SapGit/service_doc_processor_TDM/backend/Contracts/Amakhala/Amakhala Game Reserve_STO 35_Rates valid till Aug 2026.pdf"
# test_prop = "Amakhala Game Reserve"
# test_seasons = ['Low Season', 'High Season']
# test_validity_period = ['04/01/2025', '31/05/2026']
# test_room = "Bush Lodge"

test_pdf = "C:/Users/<USER>/Desktop/Sapconet/SapGit/service_doc_processor_TDM/backend/Contracts/Unlimited Destinations/Massinga beach Lodge/2025 - Massinga Beach - USD - STO 30% RATES.pdf"
test_prop = "Massinga Beach"
test_seasons = ['Low Season', 'High Season'] 
test_validity_period = ['01/01/2025', '06/01/2026']
test_room = "Presidential Villa"

#return_Document_Chunks(test_pdf)

pdfstr = document_processor.read_pdf_to_string(test_pdf, force_ocr=True)

doc_info = test_doc(pdfstr)
if doc_info["is_valid"]:
    property_info = test_property(pdfstr,test_prop, test_seasons,test_validity_period)
    if property_info["is_valid"]:
        room_info = test_room_type(pdfstr,test_prop,test_room,property_info["checked_periods"],property_info["meal_types"])
with open("C:/Users/<USER>/Desktop/Sapconet/SapGit/service_doc_processor_TDM/test_dump_aha_gemini.txt","w") as f:
    for key, value in doc_info.items():
        f.write(f"{key}: {value}\n")
    if doc_info["is_valid"]:
        f.write("\n\n")
        for key, value in property_info.items():
            f.write(f"{key}: {value}\n")
        if property_info["is_valid"]:
            f.write("\n\n")
            for key, value in room_info.items():
                f.write(f"{key}: {value}\n")
    