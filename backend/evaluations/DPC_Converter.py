import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from helpers.Document_Processing import Doc_Processor
from helpers.LLM_Handler import LLM_Handler

def main(file_path):
    #if file path is folder, read every excel file in folder
    if os.path.isdir(file_path):
        # Get all excel files in the directory
        excel_files = [f for f in os.listdir(file_path) if f.endswith(('.xlsx', '.xls'))]
        for excel_file in excel_files:
            excel_file_path = os.path.join(file_path, excel_file)
            print(f"Processing {excel_file}...")
            excelstr = Doc_Processor().read_excel_to_string(excel_file_path)
            filename = os.path.basename(excel_file_path)
            with open(filename.replace(".xlsx", ".md"), "w") as f:
                f.write(excelstr)
            print(f"File {filename} has been converted to markdown and saved as {filename.replace('.xlsx', '.md')}")
    else:
        # Process single file
        excel_file_path = file_path
        excelstr = Doc_Processor().read_excel_to_string(excel_file_path)
        filename = os.path.basename(excel_file_path)
        with open(filename.replace(".xlsx", ".md"), "w") as f:
            f.write(excelstr)
        print(f"File {filename} has been converted to markdown and saved as {filename.replace('.xlsx', '.md')}")



    # prompt = """You will receive the simplified text content of an excel sheet, broken down row-by-row. 
    # Your goal is to format the text into markdown in a presentable way for a human or LLM to parse. Accuracy is key - the result must contain all the information of the original text. 
    # However, if sections of data need not be tabular, and would make more sense in markdown as paragraphs or headings, format the result in this way."""
    # result = LLM_Handler().sendMessageToLLM(excelstr, prompt)
    # with open(filename.replace(".xlsx", "_gemini_formatted.md"), "w") as f:
    #     f.write(result)

    print(f"File {filename} has been converted to markdown and saved as {filename.replace('.xlsx', '.md')}")

if __name__=="__main__":
    path = "C:/Users/<USER>/OneDrive - Sapconet (Pty) Ltd/Service Doc Processor/DPC/More/"
    main(path)