import sys
import os
import csv
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from helpers.LLM_Handler import LLM_Handler

import matplotlib.pyplot as plt

from collections import defaultdict


#------------------------------------------------ LLM CONFIRMATIONS ------------------------------------------------
Confirmation_Prompt = """You are an experienced verification agent assisting in data entry and auditing for a large company. 
Your role is to identify any inconsistancies within data uploaded into the system compared to the source document. You will be given a single fact or claim to verify at a time.
If the data in the provided document is an exact match with the claim, return MATCHES. (Consider it a match even if the wording is slightly different)
If the claim contains correct information but with some differences or uncertainty, return PARTIAL_MATCH.
If the claim is PROVEN beyond all doubt to be incorrect based on the data, return CONTRADICTS.
If the data cannot be used to prove or disprove the claim, return UNSUBSTANTIATED.
You may provide a short paragraph of rationalisation, but must finish your response with only one of these single word answers.
Focus on the core claim being made. 
For example, if the claim is "The meal type 'bed-and-breakfast' is available for the room type 'standard room with balcony' at the property", the core claim is that the room includes breakfast, not whether it has a balcony.
However, if the claim was "The room type 'standard room with balcony' exists at the property", then you could validate whether a standard room with a balcony is mentioned.
Your goal is to flag individual problems if they exist, not nitpick unrelated details in the surrounding text.

Example:
Prompt: "The meal type "Bed and Breakfast" is available for the room type "Standard Room - Drakensburg View" at the property "Radisson Hotel Hoedspruit""
Context: "
        Data extracted from sheet: A1-Radisson Safari Hotel Hoedsp
        Data extracted from file: RAD035.xlsx
        Number of rows: 232
        Number of columns: 13

        
Row 1:
  - Columns A - M: TDM Data (TDM Cost Only) (Source System: South Africa)
Validity Dates: 1-Nov-2024 - 31-Dec-2025

Row 2:
  - Columns A - M: Accommodation Service

Row 3:
  - Columns A - M: Date Generated: 22-May-2025

Row 4:
  - Columns A - M: Radisson Safari Hotel Hoedspruit

Row 6:
  - Columns A - M: Supplier Information

Row 7:
  - Columns A - B: Supplier Code
  - Columns C - M: RAD035

Row 8:
  - Columns A - B: Supplier Groups
  - Columns C - M: Rezidor

Row 9:
  - Columns A - B: Grading
  - Columns C - M: 5 Star

Row 10:
  - Columns A - B: Country & Location
  - Columns C - M: South Africa, Limpopo Province, Hoedspruit

Row 11:
  - Columns A - M: Contact Details

Row 12:
  - Columns A - B: Telephone
  - Columns C - M: N/A

Row 13:
  - Columns A - B: Mobile
  - Columns C - M: 27 76 850 6892

Row 14:
  - Columns A - B: Facsimile
  - Columns C - M: N/A

Row 15:
  - Columns A - B: Email
  - Columns C - M: <EMAIL>

Row 16:
  - Columns A - B: Website
  - Columns C - M: https://www.radissonhotels.com/en-us/hotels/radisson-safari

Row 17:
  - Columns A - B: Address
  - Columns C - M: Radisson entrance circle 
 Main Road R527 
 Hoedspruit 
 Limpopo Province South Africa 
 1380

Row 18:
  - Columns A - B: Latitude
  - Columns C - M: -24.355025931362075 (Decimal Degrees Format

Row 19:
  - Columns A - B: Longitude
  - Columns C - M: 30.950509850292985 (Decimal Degrees Format

Row 20:
  - Columns A - B: Zone Transfer
  - Columns C - M: On Req

Row 21:
  - Columns A - M: Product Description

Row 22:
  - Columns A - M: The hotel boasts 138 exquisite rooms, meeting and event spaces, 3 restaurants, 2 swimming pools, Amani Spa and Fitness Center as well as a kid's club with mini water park
 just to name a few of the facilities available at the hotel.

Row 24:
  - Columns A - M: Supplier Rates

Row 25:
  - Columns A - M: Standard Room Garden Facing - BB Bed and Breakfast (Tourplan Option: 119308

Row 26:
  - Column A: Validity From
  - Column B: Validity To
  - Column C: Min Stay
  - Column D: CUR
  - Column E: Single Rates"

Ideal response:
The name of the property (Radisson Safari Hotel) differs slightly but is clearly the same hotel. The room type also differs slightly - "Drakensburg mountain view" - but is clearly referring to the same thing.
As the core of the claim is that the room is bed-and-breakfast, the claim is true. 
MATCHES
"""

Verification_Prompt = """You are an experienced verification agent assisting in data entry checking for a tourism company.
You will be provided the work of junior agents, who have identified what they believe are potential discrepancies in a csv. You have learned that these junior workers can be a bit overeager in identifying non-existant problems.
They will have categorised the issue as either a PARTIAL_MATCH or CONTRADICTION.
You will be provided the data as well as their explanation of the identified problem.
You can provide a paragraph of feedback before making the decision of whether to leave their verdict UNCHANGED, or to update the category to be one of the following:
If the data is acceptable, return MATCHES. (Consider it a match even if the wording is slightly different)
If the claim contains correct information but with some differences or uncertainty, return PARTIAL_MATCH.
If the claim is PROVEN beyond all doubt to be incorrect based on the data, return CONTRADICTS.
If the data cannot be used to prove or disprove the claim, return UNSUBSTANTIATED.

Example:
Prompt: "
Item: ('Radisson Blu Hotel, Waterfront', 'Collection Superior Room - Balcony', 'B&B - Bed & Breakfast')
Status: PARTIAL_MATCH
CSV Claim: The meal type 'B&B - Bed & Breakfast' is available for the room type 'Collection Superior Room - Balcony' at the property 'Radisson Blu Hotel, Waterfront'.
Explanation: The claim states that the meal type 'B&B - Bed & Breakfast' is available for the room type 'Collection Superior Room - Balcony' at the property 'Radisson Blu Hotel, Waterfront'. However, the data indicates the existence of a 'Collection Superior Room' but it specifically mentions 'Collection Superior Room - BB Bed and Breakfast', not the term 'B&B - Bed & Breakfast'. Additionally, the property is referred to as 'Radisson Collection Hotel Waterfront Cape Town', which is not the same as 'Radisson Blu Hotel, Waterfront'. Therefore, while the room type has a breakfast option, the specific naming and property identification do not match precisely.
"
Ideal Response:
This is clearly just a slight difference in typesetting, not an actual problem.
MATCHES
"""

# Explanation_Prompt = """You are helping to explain discrepancies between extracted CSV data and source documents.
# Given a claim from CSV data and the source document text, explain why there is a mismatch.
# Be specific about what the CSV contains versus what the document actually says.
# Keep your explanation concise but informative - focus on the key differences.
# If it's a partial match, explain what parts match and what parts don't.
# If it's a contradiction, clearly state what the document says instead."""

llmtool = LLM_Handler()

def AskLLMToConfirm(source, claim, max_tries=3):
    for tries in range(max_tries):
        response = llmtool.sendMessagesToLLM(["Claim to verify:\n" + claim,"Document source:\n" + source], Confirmation_Prompt)
        if isinstance(response, tuple):
            response = response[0]

        if isinstance(response, str):
            label = __get_label_from_text(response)
            if label:
                return label, response
            
            
        print("unexpected confirmation response:", response)
    print("3 tries with no success")
    return "?", response

def __get_label_from_text(text):
    # Find last occurrence of each label
    response_upper = text.upper()
    matches_pos = response_upper.rfind("MATCHES")
    partial_pos = response_upper.rfind("PARTIAL_MATCH")
    contradicts_pos = response_upper.rfind("CONTRADICTS")
    unsubstantiated_pos = response_upper.rfind("UNSUBSTANTIATED")
    
    # Get the last occurring label
    positions = {
        matches_pos: "MATCHES",
        partial_pos: "PARTIAL_MATCH",
        contradicts_pos: "CONTRADICTS",
        unsubstantiated_pos: "UNSUBSTANTIATED"
    }
    
    # Filter out -1 (not found) and get the last occurring label
    valid_positions = {pos: label for pos, label in positions.items() if pos != -1}
    if valid_positions:
        last_label = positions[max(valid_positions.keys())]
        return last_label
    return None

def AskLLMToValidate(label, distinct_dict_key, claim, explanation, max_tries=3):
    for tries in range(max_tries):
        verification_response = llmtool.sendMessagesToLLM([
                f"Item: {distinct_dict_key}\nStatus: {label}\nCSV Claim: {claim}\nExplanation: {explanation}"
            ], Verification_Prompt)
            
        if isinstance(verification_response, tuple):
            verification_response = verification_response[0]
        
        # Check if verification suggests a different category
        if isinstance(verification_response, str):
            label = __get_label_from_text(verification_response)
            if label:
                return label, verification_response
       
    print("3 tries with no success")
    return label, verification_response
    

# def AskLLMToExplain(source, claim, label):
#     """Get explanation for why a claim doesn't match the source"""
#     explanation_request = f"The claim '{claim}' was labeled as {label}. Please explain the discrepancy."
#     response = llmtool.sendMessagesToLLM([explanation_request, "Document source:\n" + source], Explanation_Prompt)
#     if isinstance(response, tuple):
#         response = response[0]
#     return response

#------------------------------------------------ Enhanced Analytics ------------------------------------------------

class ValidationAnalytics:
    def __init__(self):
        self.category_counts = defaultdict(lambda: [0, 0, 0, 0])  # [matches, partial, contradicts, unsubstantiated]
        self.detailed_results = defaultdict(dict)
        self.explanations = []
        
    def add_result(self, category, key, label, claim="", explanation="", validation_explanation=""):
        """Add a validation result to analytics"""
        self.detailed_results[category][key] = {
            'label': label,
            'claim': claim,
            'explanation': explanation,
            'validation_explanation': validation_explanation
        }
        
        # Update counts
        label_index = {'MATCHES': 0, 'PARTIAL_MATCH': 1, 'CONTRADICTS': 2, 'UNSUBSTANTIATED': 3}
        if label in label_index:
            self.category_counts[category][label_index[label]] += 1
            
        # Store explanations for non-matches
        if label in ['PARTIAL_MATCH', 'CONTRADICTS'] and explanation and validation_explanation:
            self.explanations.append({
                'category': category,
                'key': str(key),
                'label': label,
                'claim': claim,
                'explanation': explanation,
                'validation_explanation': validation_explanation
            })
    
    def get_overall_stats(self):
        """Calculate overall statistics across all categories"""
        total_counts = [0, 0, 0, 0]
        for counts in self.category_counts.values():
            for i in range(4):
                total_counts[i] += counts[i]
        
        total = sum(total_counts)
        if total == 0:
            return total_counts, [0, 0, 0, 0]
            
        percentages = [round((count / total) * 100, 1) for count in total_counts]
        return total_counts, percentages
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        total_counts, percentages = self.get_overall_stats()
        total = sum(total_counts)
        
        report = "="*60 + "\n"
        report += "VALIDATION SUMMARY REPORT\n"
        report += "="*60 + "\n\n"
        
        report += f"OVERALL STATISTICS (Total Items: {total})\n"
        report += "-" * 40 + "\n"
        report += f"✓ MATCHES: {total_counts[0]} ({percentages[0]}%)\n"
        report += f"~ PARTIAL_MATCH: {total_counts[1]} ({percentages[1]}%)\n"
        report += f"✗ CONTRADICTS: {total_counts[2]} ({percentages[2]}%)\n"
        report += f"? UNSUBSTANTIATED: {total_counts[3]} ({percentages[3]}%)\n\n"
        
        # Category breakdown
        report += "CATEGORY BREAKDOWN\n"
        report += "-" * 40 + "\n"
        for category, counts in self.category_counts.items():
            cat_total = sum(counts)
            if cat_total > 0:
                cat_percentages = [round((count / cat_total) * 100, 1) for count in counts]
                report += f"\n{category.upper().replace('_', ' ')} ({cat_total} items):\n"
                report += f"  ✓ Matches: {counts[0]} ({cat_percentages[0]}%)\n"
                report += f"  ~ Partial: {counts[1]} ({cat_percentages[1]}%)\n"
                report += f"  ✗ Contradicts: {counts[2]} ({cat_percentages[2]}%)\n"
                report += f"  ? Unsubstantiated: {counts[3]} ({cat_percentages[3]}%)\n"
        
        return report
    
    def save_explanations(self, filename="validation_explanations.txt"):
        """Save detailed explanations for non-matching items"""
        if not self.explanations:
            return
            
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("DETAILED EXPLANATIONS FOR NON-MATCHING ITEMS\n")
            f.write("="*60 + "\n\n")
            
            # Group by category
            by_category = defaultdict(list)
            for exp in self.explanations:
                by_category[exp['category']].append(exp)
            
            for category, items in by_category.items():
                f.write(f"\n{category.upper().replace('_', ' ')}\n")
                f.write("-" * 40 + "\n")
                
                for item in items:
                    f.write(f"\nItem: {item['key']}\n")
                    f.write(f"Status: {item['label']}\n")
                    f.write(f"CSV Claim: {item['claim']}\n")
                    f.write(f"Explanation: {item['explanation']}\n")
                    f.write(f"validation_explanation: {item['validation_explanation']}\n")
                    f.write("-" * 20 + "\n")
    
    def create_visualization(self, save_path="validation_results.png"):
        """Create bar chart visualization of results"""
        try:
            categories = list(self.category_counts.keys())
            if not categories:
                print("No data to visualize")
                return
            
            # Prepare data for plotting
            matches = [self.category_counts[cat][0] for cat in categories]
            partial = [self.category_counts[cat][1] for cat in categories]
            contradicts = [self.category_counts[cat][2] for cat in categories]
            unsubstantiated = [self.category_counts[cat][3] for cat in categories]
            
            # Create stacked bar chart
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # Category breakdown
            x_pos = range(len(categories))
            width = 0.6
            
            p1 = ax1.bar(x_pos, matches, width, label='Matches', color='#2E8B57')
            p2 = ax1.bar(x_pos, partial, width, bottom=matches, label='Partial Match', color='#FFD700')
            p3 = ax1.bar(x_pos, contradicts, width, bottom=[m+p for m,p in zip(matches, partial)], 
                        label='Contradicts', color='#DC143C')
            p4 = ax1.bar(x_pos, unsubstantiated, width, 
                        bottom=[m+p+c for m,p,c in zip(matches, partial, contradicts)], 
                        label='Unsubstantiated', color='#808080')
            
            ax1.set_xlabel('Categories')
            ax1.set_ylabel('Number of Items')
            ax1.set_title('Validation Results by Category')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels([cat.replace('_', ' ').title() for cat in categories], rotation=45, ha='right')
            ax1.legend()
            ax1.grid(axis='y', alpha=0.3)
            
            # Overall pie chart
            total_counts, percentages = self.get_overall_stats()
            labels = ['Matches', 'Partial Match', 'Contradicts', 'Unsubstantiated']
            colors = ['#2E8B57', '#FFD700', '#DC143C', '#808080']
            
            # Only show non-zero slices
            non_zero_data = [(count, label, color, pct) for count, label, color, pct in 
                           zip(total_counts, labels, colors, percentages) if count > 0]
            
            if non_zero_data:
                counts, labels, colors, percentages = zip(*non_zero_data)
                wedges, texts, autotexts = ax2.pie(counts, labels=labels, colors=colors, autopct='%1.1f%%', 
                                                  startangle=90)
                ax2.set_title('Overall Validation Results Distribution')
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Visualization saved to {save_path}")
            
        except Exception as e:
            print(f"Error creating visualization: {e}")

#------------------------------------------------ Response parsing and display ------------------------------------------------

def updateValidityTuple(tuple, label):
    if label=="MATCHES":
        tuple[0] +=1
    elif label=="PARTIAL_MATCH":
        tuple[1] +=1
    elif label=="CONTRADICTS":
        tuple[2] +=1
    elif label=="UNSUBSTANTIATED":
        tuple[3] +=1
    
    return tuple

def displayValidityTuple(contents, tuple, typename):
    matches = [key for key, value in contents.items() if value == "MATCHES"]
    partial_matches = [key for key, value in contents.items() if value == "PARTIAL_MATCH"]
    contradicts = [key for key, value in contents.items() if value == "CONTRADICTS"]
    unsubstantiated = [key for key, value in contents.items() if value == "UNSUBSTANTIATED"]
   
    toPrint = "--------\n"
    toPrint += typename + " confirmed to be present: " + str(tuple[0]) + "\n"
    toPrint += "     " + str(matches) + "\n"
    toPrint += typename + " partial match: " + str(tuple[1]) + "\n"
    toPrint += "     " + str(partial_matches) + "\n"
    toPrint += typename + " confirmed not in the doc: " + str(tuple[2]) + "\n"
    toPrint += "     " + str(contradicts) + "\n"
    toPrint += typename + " not found anywhere: " + str(tuple[3]) + "\n"
    toPrint += "     " + str(unsubstantiated) + "\n"
    
    print(toPrint)
    return toPrint

def load_csv(file_path):
    
    data = []
    try:
        with open(file_path, mode='r', encoding='utf-8-sig') as csv_file:  # <-- use utf-8-sig here
            csv_reader = csv.DictReader(csv_file)
            print("Headers:", csv_reader.fieldnames)
            for row in csv_reader:
                data.append(row)
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except Exception as e:
        print(f"Error reading CSV file: {e}")
    return data


def confirmRowElement(distinct_dict, distinct_dict_key, validity_count_dict, claim, pdfstr, 
                     analytics, category, generate_explanations=False):
    """
    Confirms the presence of a row element in the corresponding pdf, using a provided claim string to verify against the document
    Enhanced version that includes analytics tracking and optional explanation generation
    """
    if distinct_dict_key not in distinct_dict:
        # Get initial label and explanation
        label, explanation = AskLLMToConfirm(pdfstr, claim)
        
        # If initial label is partial match or contradiction, verify it
        if label in ['PARTIAL_MATCH', 'CONTRADICTS']:
            label, validation_explanation = AskLLMToValidate(label=label,distinct_dict_key=distinct_dict_key,claim=claim,explanation=explanation)
        else:
            validation_explanation=""
        
        distinct_dict[distinct_dict_key] = label
        validity_count_dict = updateValidityTuple(validity_count_dict, label)
        
        # Generate explanation for non-matches if requested
        # explanation = ""
        # if generate_explanations and label in ['PARTIAL_MATCH', 'CONTRADICTS']:
        #     try:
        #         explanation = AskLLMToExplain(pdfstr, claim, label)
        #     except Exception as e:
        #         explanation = f"Could not generate explanation: {e}"
        
        # Add to analytics
        if analytics is not None:
            analytics.add_result(category, distinct_dict_key, label, claim, explanation, validation_explanation)
        

    return distinct_dict, validity_count_dict



def validate_csv(pdfstr, csv_path, result_file_name="search_eval_results.txt", 
                generate_explanations=True, create_visualization=True):
    """
    Enhanced validation function with analytics and explanations
    """
    
    csv = load_csv(csv_path)
    analytics = ValidationAnalytics()
    
    # Initialize validity counts
    property_validity_counts = [0,0,0,0]
    room_type_validity_counts =  [0,0,0,0]
    meal_basis_validity_counts =  [0,0,0,0]
    includes_validity_counts =  [0,0,0,0]
    excludes_validity_counts =  [0,0,0,0]
    age_range_1_validity_counts =  [0,0,0,0]
    age_range_2_validity_counts =  [0,0,0,0]
    max_adults_validity_counts =  [0,0,0,0]
    max_children_validity_counts =  [0,0,0,0]
    max_capacity_validity_counts =  [0,0,0,0]
    policy_range_1_validity_counts = [0,0,0,0]
    policy_range_2_validity_counts = [0,0,0,0]
    pay_stay_validity_counts = [0,0,0,0]
    period_validity_counts = [0,0,0,0]
    period_start_validity_counts = [0,0,0,0]
    period_end_validity_counts = [0,0,0,0]
    child_rate_1_validity_counts = [0,0,0,0]
    child_rate_2_validity_counts = [0,0,0,0]
    single_room_rate_validity_counts = [0,0,0,0]
    double_room_rate_validity_counts = [0,0,0,0]
    triple_room_rate_validity_counts = [0,0,0,0]
    quad_room_rate_validity_counts = [0,0,0,0]
    infant_rate_validity_counts = [0,0,0,0]

  


    # Initialize distinct dictionaries
    distinct_properties = {}
    distinct_room_types = {}
    distinct_meal_basis = {}
    distinct_includes = {}
    distinct_excludes = {}
    distinct_age_range_1 = {}
    distinct_age_range_2 = {}
    distinct_max_adults = {}
    distinct_max_children = {}
    distinct_max_capacity = {}
    distinct_policy_range_1 = {}
    distinct_policy_range_2 = {}
    distinct_pay_stay = {}
    distinct_period = {}
    distinct_child_rate_1 = {}
    distinct_child_rate_2 = {}
    distinct_single_room_rate = {}
    distinct_double_room_rate = {}
    distinct_triple_room_rate = {}
    distinct_quad_room_rate = {}
    distinct_infant_rate = {}

    for row in csv:
        if row.get('Property'):
            # Confirm property
            property_key = (row['Property'],)
            distinct_properties, property_validity_counts = confirmRowElement(
                distinct_properties, property_key, property_validity_counts, f"The property '{row['Property']}' exists in the document.", pdfstr, analytics, "property", generate_explanations
            )

            if row.get('Room type'):
                # Confirm room type
                room_key = (row['Property'], row['Room type'])
                distinct_room_types, room_type_validity_counts = confirmRowElement(
                    distinct_room_types, room_key, room_type_validity_counts, f"The room type '{row['Room type']}' exists at the property '{row['Property']}'", pdfstr, analytics, "room type", generate_explanations
                )

                if row.get('Meal basis'):
                    meal_basis_key = (row['Property'], row['Room type'], row['Meal basis'])
                    distinct_meal_basis, meal_basis_validity_counts = confirmRowElement(
                        distinct_meal_basis, meal_basis_key, meal_basis_validity_counts, f"The meal type '{row['Meal basis']}' is available for the room type '{row['Room type']}' at the property '{row['Property']}'.", pdfstr, analytics, "meal basis", generate_explanations
                    )

                if row.get('Includes'):
                    includes_key = (row['Property'], row['Room type'], row['Includes'])
                    distinct_includes, includes_validity_counts = confirmRowElement(
                        distinct_includes, includes_key, includes_validity_counts, f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) includes the following: {row['Includes']}.", pdfstr, analytics, "includes", generate_explanations
                    )

                if row.get('Excludes'):
                    excludes_key = (row['Property'], row['Room type'], row['Excludes'])
                    distinct_excludes, excludes_validity_counts = confirmRowElement(
                        distinct_excludes, excludes_key, excludes_validity_counts, f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) excludes the following: {row['Excludes']}.", pdfstr, analytics, "excludes", generate_explanations
                    )

                if row.get('Child 1 From age') and row.get('Child 1 To age'):
                    age_range_key_1 = (row['Property'], row['Child 1 From age'], row['Child 1 To age'])
                    distinct_age_range_1, age_range_1_validity_counts = confirmRowElement(
                        distinct_age_range_1, age_range_key_1, age_range_1_validity_counts, f"The following age range for a category of child is defined at the property '{row['Property']}': {row['Child 1 From age']}-{row['Child 1 To age']}", pdfstr, analytics, "age range 1", generate_explanations
                    )

                if row.get('Child 2 From age') and row.get('Child 2 To age'):
                    age_range_key_2 = (row['Property'], row['Child 2 From age'], row['Child 2 To age'])
                    distinct_age_range_2, age_range_2_validity_counts = confirmRowElement(
                        distinct_age_range_2, age_range_key_2, age_range_2_validity_counts, f"The following age range for a category of child is defined at the property '{row['Property']}': {row['Child 2 From age']}-{row['Child 2 To age']}", pdfstr, analytics, "age range 2", generate_explanations
                    )

                if row.get('Max Adults'):
                    max_adults_key = (row['Property'], row['Room type'], row['Max Adults'])
                    distinct_max_adults, max_adults_validity_counts = confirmRowElement(
                        distinct_max_adults, max_adults_key, max_adults_validity_counts, f"The room type '{row['Room type']}' at the property '{row['Property']}' will allow up to {row['Max Adults']} adults", pdfstr, analytics, "max adults", generate_explanations
                    )

                if row.get('Max Children'):
                    max_children_key = (row['Property'], row['Room type'], row['Max Children'])
                    if row['Max Children'] in ["0","-1"]:
                        text = f"The room type '{row['Room type']}' at the property '{row['Property']}' will accommodate no children"
                    else: text = f"The room type '{row['Room type']}' at the property '{row['Property']}' will allow up to {row['Max Children']} children"
                    distinct_max_children, max_children_validity_counts = confirmRowElement(
                        distinct_max_children, max_children_key, max_children_validity_counts, text, pdfstr, analytics, "max children", generate_explanations
                    )

                if row.get('Max A+C '):
                    max_capacity_key = (row['Property'], row['Room type'], row['Max A+C '])
                    distinct_max_capacity, max_capacity_validity_counts = confirmRowElement(
                        distinct_max_capacity, max_capacity_key, max_capacity_validity_counts, f"The room type '{row['Room type']}' at the property '{row['Property']}' has the max capacity {row['Max A+C ']} (maximum being inclusive of both adults and children)", pdfstr, analytics, "max capacity", generate_explanations
                    )

                if row.get('Cancellation Policy from days 1') and row.get('Cancellation fee % 1'):
                    if not (row['Cancellation Policy from days 1'] == "0" and row['Cancellation Policy from days 1'] =="0"):
                        policy_range_key_1 = (
                            row['Property'], 
                            row['Cancellation Policy from days 1'], row['Cancellation fee % 1'] #adjusted to not be per room type
                        )
                        distinct_policy_range_1, policy_range_1_validity_counts = confirmRowElement(
                            distinct_policy_range_1, policy_range_key_1, policy_range_1_validity_counts, f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) has the following cancellation policy: Cancelling {row['Cancellation Policy from days 1']} days before the booking or later, your cancellation fee will be {row['Cancellation fee % 1']}%. (there may be a later date that introduces a separate policy)", pdfstr, analytics, "policy range 1", generate_explanations
                        )

                if row.get('Cancellation Policy from days 2') and row.get('Cancellation fee % 2'):
                    if not (row['Cancellation Policy from days 2'] == "0" and row['Cancellation Policy from days 2'] =="0"):
                        policy_range_key_2 = (
                            row['Property'], 
                            row['Cancellation Policy from days 2'], row['Cancellation fee % 2']
                        )
                        distinct_policy_range_2, policy_range_2_validity_counts = confirmRowElement(
                            distinct_policy_range_2, policy_range_key_2, policy_range_2_validity_counts, f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) has the following cancellation policy: Cancelling {row['Cancellation Policy from days 2']} cancellation policy, days before the booking or later, your cancellation fee will be {row['Cancellation fee % 2']}", pdfstr, analytics, "policy range 2", generate_explanations
                        )
                # handle ,Pay stay days 1,Pay stay days 2,Pay stay days 3,Pay stay free nights 1,Pay stay free nights 2,Pay stay free nights 3,
                if row.get('Pay stay days 1') and row.get("Pay stay free nights 1"):
                    if not (row['Pay stay days 1'] == "0" and row['Pay stay free nights 1'] =="0"):
                        pay_stay_key_1 = (row['Property'], row['Room type'], row['Pay stay days 1'], row["Pay stay free nights 1"])
                        pay_stay_explanation = f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) has the following pay stay offer: Stay for {row['Pay stay days 1']} nights and get {row['Pay stay free nights 1']} nights free."
                        try:
                            pay_days = int(row['Pay stay days 1'])
                            free_nights = int(row['Pay stay free nights 1'])
                            paid_days = pay_days - free_nights
                            pay_stay_explanation += f"In other words, you pay {paid_days} and stay {row['Pay stay days 1']} nights."
                        except ValueError:
                            # If conversion to int fails, use original explanation
                            pass
                        distinct_pay_stay, pay_stay_validity_counts = confirmRowElement(
                            distinct_pay_stay, pay_stay_key_1, pay_stay_validity_counts, pay_stay_explanation, pdfstr, analytics, "pay stay", generate_explanations
                        )
                if row.get('Pay stay days 2') and row.get("Pay stay free nights 2"):
                    if not (row['Pay stay days 2'] == "0" and row['Pay stay free nights 2'] =="0"):
                        pay_stay_key_2 = (row['Property'], row['Room type'], row['Pay stay days 2'], row["Pay stay free nights 2"])
                        pay_stay_explanation = f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) has the following pay stay offer: Stay for {row['Pay stay days 2']} nights and get {row['Pay stay free nights 2']} nights free."
                        try:
                            pay_days = int(row['Pay stay days 2'])
                            free_nights = int(row['Pay stay free nights 2'])
                            paid_days = pay_days - free_nights
                            pay_stay_explanation += f"In other words, you pay {paid_days} and stay {row['Pay stay days 2']} nights."
                        except ValueError:
                            # If conversion to int fails, use original explanation
                            pass
                        distinct_pay_stay, pay_stay_validity_counts = confirmRowElement(
                            distinct_pay_stay, pay_stay_key_2, pay_stay_validity_counts, pay_stay_explanation, pdfstr, analytics, "pay stay", generate_explanations
                        )
                if row.get('Pay stay days 3') and row.get("Pay stay free nights 3"):
                    if not (row['Pay stay days 3'] == "0" and row['Pay stay free nights 3'] =="0"):
                        pay_stay_explanation = f"The property '{row['Property']}' (and specifically the room '{row['Room type']}', although this information may not be needed) has the following pay stay offer: Stay for {row['Pay stay days 3']} nights and get {row['Pay stay free nights 3']} nights free."
                        try:
                            pay_days = int(row['Pay stay days 3'])
                            free_nights = int(row['Pay stay free nights 3'])
                            paid_days = pay_days - free_nights
                            pay_stay_explanation += f"In other words, you pay {paid_days} and stay {row['Pay stay days 3']} nights."
                        except ValueError:
                            # If conversion to int fails, use original explanation
                            pass
                        pay_stay_key_3 = (row['Property'], row['Room type'], row['Pay stay days 3'], row["Pay stay free nights 3"])
                        distinct_pay_stay, pay_stay_validity_counts = confirmRowElement(
                            distinct_pay_stay, pay_stay_key_3, pay_stay_validity_counts, pay_stay_explanation, pdfstr, analytics, "pay stay", generate_explanations
                        )


                # if row.get('Period'):
                #     period_key = (row['Property'], row['Room type'], row['Period'])
                #     distinct_period, period_validity_counts = confirmRowElement(
                #         distinct_period, period_key, period_validity_counts, f"The period '{row['Period']}' exists in the document.", pdfstr,
                #         analytics, "period", generate_explanations
                #     )
                #     if row.get("DATE_FROM"):
                #         period_start_key = (row['Property'], row['Room type'], row['Period'], row['DATE_FROM'])
                #         distinct_period_start, period_start_validity_counts = confirmRowElement(
                #             distinct_period_start, period_start_key, period_start_validity_counts, f"The period '{row['Period']}' starts on '{row['DATE_FROM']}' according to the document.", pdfstr,
                #             analytics, "period start", generate_explanations
                #         )
                #     if row.get("DATE_TO"):
                #         period_end_key = (row['Property'], row['Room type'], row['Period'], row['DATE_TO'])
                #         distinct_period_end, period_end_validity_counts = confirmRowElement(
                #             distinct_period_end, period_end_key, period_end_validity_counts, f"The period '{row['Period']}' ends on '{row['DATE_TO']}' according to the document.", pdfstr,
                #             analytics, "period end", generate_explanations
                #         )
                if row.get("DATE_FROM") and row.get("DATE_TO"):
                    period_key = (row['Property'], row['Room type'], row['DATE_FROM'], row["DATE_TO"])
                    distinct_period, period_validity_counts = confirmRowElement(
                        distinct_period, period_key, period_validity_counts, f"There is a period at {row['Property']} that is defined from {row.get("DATE_FROM")} to {row.get("DATE_TO")} in the document.", pdfstr,
                        analytics, "period", generate_explanations
                    )
                
                if row.get("Child rate 1"):
                    if (row['Child rate 1']!="-1"):
                        msg = f"The child rate '{row['Child rate 1']}' is applicable for the room type '{row['Room type']}' at the property '{row['Property']}'."
                    else: msg=f"There is no child rate applicable for the room type '{row['Room type']}' at the property '{row['Property']} - eg because children are not allowed'."
                    child_rate_key_1 = (row['Property'], row['Room type'], row['Child rate 1'])
                    distinct_child_rate_1, child_rate_1_validity_counts = confirmRowElement(
                        distinct_child_rate_1, child_rate_key_1, child_rate_1_validity_counts, msg, pdfstr,
                        analytics, "child rate 1", generate_explanations
                    )
                if row.get("Child rate 2"):
                    if (row['Child rate 2']!="-1"):
                        msg = f"The child rate '{row['Child rate 2']}' is applicable for a second child in the room type '{row['Room type']}' at the property '{row['Property']}'."
                    else: msg=f"There is no child rate for a second child applicable for the room type '{row['Room type']}' at the property '{row['Property']} - eg because multiple children are not allowed'."
                    child_rate_key_2 = (row['Property'], row['Room type'], row['Child rate 2'])
                    distinct_child_rate_2, child_rate_2_validity_counts = confirmRowElement(
                        distinct_child_rate_2, child_rate_key_2, child_rate_2_validity_counts, msg, pdfstr,
                        analytics, "child rate 2", generate_explanations
                    )
                if row.get("Single room rate"):
                    single_room_rate_key = (row['Property'], row['Room type'], row['Single room rate'])
                    distinct_single_room_rate, single_room_rate_validity_counts = confirmRowElement(
                        distinct_single_room_rate, single_room_rate_key, single_room_rate_validity_counts, f"The single room rate '{row['Single room rate']}' is applicable for the room type '{row['Room type']}' at the property '{row['Property']}'.", pdfstr,
                        analytics, "single room rate", generate_explanations
                    )
                if row.get("Double room rate"):
                    double_room_rate_key = (row['Property'], row['Room type'], row['Double room rate'])
                    distinct_double_room_rate, double_room_rate_validity_counts = confirmRowElement(
                        distinct_double_room_rate, double_room_rate_key, double_room_rate_validity_counts, f"The double room rate '{row['Double room rate']}' is applicable for the room type '{row['Room type']}' at the property '{row['Property']}'.", pdfstr,
                        analytics, "double room rate", generate_explanations
                    )
                if row.get("Triple room rate"):
                    triple_room_rate_key = (row['Property'], row['Room type'], row['Triple room rate'])
                    distinct_triple_room_rate, triple_room_rate_validity_counts = confirmRowElement(
                        distinct_triple_room_rate, triple_room_rate_key, triple_room_rate_validity_counts, f"The triple room rate '{row['Triple room rate']}' is applicable for the room type '{row['Room type']}' at the property '{row['Property']}'.", pdfstr,
                        analytics, "triple room rate", generate_explanations
                    )
                if row.get("Quad room rate"):
                    quad_room_rate_key = (row['Property'], row['Room type'], row['Quad room rate'])
                    distinct_quad_room_rate, quad_room_rate_validity_counts = confirmRowElement(
                        distinct_quad_room_rate, quad_room_rate_key, quad_room_rate_validity_counts, f"The quad room rate '{row['Quad room rate']}' is applicable for the room type '{row['Room type']}' at the property '{row['Property']}'.", pdfstr,
                        analytics, "quad room rate", generate_explanations
                    )
                if row.get("Infant rate"):
                    infant_rate_key = (row['Property'], row['Room type'], row['Infant rate'])
                    distinct_infant_rate, infant_rate_validity_counts = confirmRowElement(
                        distinct_infant_rate, infant_rate_key, infant_rate_validity_counts, f"The infant rate '{row['Infant rate']}' is applicable for the room type '{row['Room type']}' at the property '{row['Property']}'.", pdfstr,
                        analytics, "infant rate", generate_explanations
                    )
                    
                    
                    

    # Generate original detailed output
    toSave = displayValidityTuple(distinct_properties, property_validity_counts, "properties")
    toSave += "\n" + displayValidityTuple(distinct_room_types,room_type_validity_counts, "room types")
    toSave += "\n" + displayValidityTuple(distinct_meal_basis, meal_basis_validity_counts, "meal basis")
    toSave += "\n" + displayValidityTuple(distinct_includes, includes_validity_counts, "includes")
    toSave += "\n" + displayValidityTuple(distinct_excludes, excludes_validity_counts, "excludes")
    toSave += "\n" + displayValidityTuple(distinct_age_range_1, age_range_1_validity_counts, "age range 1")
    toSave += "\n" + displayValidityTuple(distinct_age_range_2, age_range_2_validity_counts, "age range 2")
    toSave += "\n" + displayValidityTuple(distinct_max_adults, max_adults_validity_counts, "max adults")
    toSave += "\n" + displayValidityTuple(distinct_max_children, max_children_validity_counts, "max children")
    toSave += "\n" + displayValidityTuple(distinct_max_capacity, max_capacity_validity_counts, "max capacity")
    toSave += "\n" + displayValidityTuple(distinct_policy_range_1, policy_range_1_validity_counts, "policy range 1")
    toSave += "\n" + displayValidityTuple(distinct_policy_range_2, policy_range_2_validity_counts, "policy range 2")
    toSave += "\n" + displayValidityTuple(distinct_pay_stay, pay_stay_validity_counts, "pay stay")
    toSave += "\n" + displayValidityTuple(distinct_period, period_validity_counts, "period")
    toSave += "\n" + displayValidityTuple(distinct_child_rate_1, child_rate_1_validity_counts, "child rate 1")
    toSave += "\n" + displayValidityTuple(distinct_child_rate_2, child_rate_2_validity_counts, "child rate 2")
    toSave += "\n" + displayValidityTuple(distinct_single_room_rate, single_room_rate_validity_counts, "single room rate")
    toSave += "\n" + displayValidityTuple(distinct_double_room_rate, double_room_rate_validity_counts, "double room rate")
    toSave += "\n" + displayValidityTuple(distinct_triple_room_rate, triple_room_rate_validity_counts, "triple room rate")
    toSave += "\n" + displayValidityTuple(distinct_quad_room_rate, quad_room_rate_validity_counts, "quad room rate")
    toSave += "\n" + displayValidityTuple(distinct_infant_rate, infant_rate_validity_counts, "infant rate")


    # Add enhanced summary
    summary_report = analytics.generate_summary_report()
    toSave = summary_report + "\n\n" + "DETAILED BREAKDOWN\n" + "="*60 + "\n" + toSave

    # Save main results
    with open(result_file_name, "w", encoding="utf-8") as f:
        f.write(toSave)
    
    # Save explanations if requested
    if generate_explanations:
        explanations_file = result_file_name.replace('.txt', '_explanations.txt')
        analytics.save_explanations(explanations_file)
        print(f"Detailed explanations saved to {explanations_file}")
    
    # Create visualization if requested
    if create_visualization:
        viz_file = result_file_name.replace('.txt', '_chart.png')
        analytics.create_visualization(viz_file)
    
    # Print summary to console
    print("\n" + summary_report)
    
    return toSave, analytics

class BatchValidator:
    def __init__(self):
        self.validation_jobs = []
        self.combined_analytics = ValidationAnalytics()
        self.job_results = []
        
    def add_job(self, pdf_text_path, csv_path, job_name=None):
        """Add a validation job to the batch"""
        if job_name is None:
            job_name = f"Job_{len(self.validation_jobs) + 1}"
        self.validation_jobs.append({
            'pdf_path': pdf_text_path,
            'csv_path': csv_path,
            'name': job_name
        })
        
    def run_batch(self, base_output_dir="validation_results", generate_explanations=True, create_visualization=True):
        """Run all validation jobs in the batch"""
        import os
        from datetime import datetime
        
        # Create timestamped output directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(base_output_dir, f"batch_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        
        # Run each job
        for job in self.validation_jobs:
            print(f"\nProcessing {job['name']}...")
            
            # Read PDF content
            with open(job['pdf_path'], 'r') as f:
                pdf_content = f.read()
            
            # Create job-specific output files
            job_output = os.path.join(output_dir, f"{job['name']}")
            results_file = f"{job_output}_results.txt"
            
            # Run validation
            results, analytics = validate_csv(
                pdf_content,
                job['csv_path'],
                result_file_name=results_file,
                generate_explanations=generate_explanations,
                create_visualization=create_visualization
            )
            
            # Store results
            self.job_results.append({
                'name': job['name'],
                'results': results,
                'analytics': analytics
            })
            
            # Merge analytics
            self._merge_analytics(analytics)
        
        # Generate combined report
        self._generate_combined_report(output_dir)
        
        return output_dir
    
    def _merge_analytics(self, analytics):
        """Merge analytics from individual jobs into combined analytics"""
        # Merge category counts
        for category, counts in analytics.category_counts.items():
            for i in range(4):
                self.combined_analytics.category_counts[category][i] += counts[i]
        
        # Merge detailed results
        for category, results in analytics.detailed_results.items():
            if category not in self.combined_analytics.detailed_results:
                self.combined_analytics.detailed_results[category] = {}
            self.combined_analytics.detailed_results[category].update(results)
        
        # Merge explanations
        self.combined_analytics.explanations.extend(analytics.explanations)
    
    def _generate_combined_report(self, output_dir):
        """Generate a combined report for all jobs"""
        # Generate combined summary
        summary_report = self.combined_analytics.generate_summary_report()
        
        # Save combined report
        combined_report_path = os.path.join(output_dir, "combined_report.txt")
        with open(combined_report_path, 'w', encoding='utf-8') as f:
            f.write("COMBINED VALIDATION REPORT\n")
            f.write("="*60 + "\n\n")
            f.write(summary_report)
        
        # Save combined explanations
        if self.combined_analytics.explanations:
            combined_explanations_path = os.path.join(output_dir, "combined_explanations.txt")
            self.combined_analytics.save_explanations(combined_explanations_path)
        
        # Create combined visualization
        combined_viz_path = os.path.join(output_dir, "combined_chart.png")
        self.combined_analytics.create_visualization(combined_viz_path)
        
        print(f"\nCombined report saved to {output_dir}")

def main():
    # Example of batch validation
    validator = BatchValidator()
    
    # Add validation jobs
    validator.add_job(
        "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD001.md",
        "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/Raddisson Blu Hotel, Waterfront_openai_output.csv",
        "Radisson_Blu_Waterfront"
    )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD003.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Blu Hotel, Sandton.csv",
    #     "Radisson_Blu_Sandton"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD006.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Blu Hotel, Gautrain.csv",
    #     "Radisson_Blu_Gautrain"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD022.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson RED Hotel, V&A Waterfront.csv",
    #     "Radisson_RED_V&A_Waterfront"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD030.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Red Rosebank.csv",
    #     "Radisson_Red_Rosebank"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD031.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Hotel & Convention Centre, Johannesburg, OR Tambo.csv",
    #     "Radisson_Hotel_Convention_Centre_Johannesburg_OR_Tambo"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD032.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Blu Hotel, Durban Umhlanga.csv",
    #     "Radisson_Blu_Durban_Umhlanga"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD035.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Safari Hotel Hoedspruit.csv",
    #     "Radisson_Safari_Hotel_Hoedspruit"
    # )
    # validator.add_job(
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/RAD037.md",
    #     "C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs/split_Radisson Hotel Cape Town Foreshore.csv",
    #     "Radisson_Hotel_Cape_Town_Foreshore"
    # )
    
    
    
    # Run batch validation
    output_dir = validator.run_batch(
        base_output_dir="validation_results",
        generate_explanations=True,
        create_visualization=True
    )
    
    print(f"\nBatch validation complete. Results saved to {output_dir}")

if __name__ == "__main__":
    main()