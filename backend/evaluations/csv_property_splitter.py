import csv
from itertools import groupby
import os

def split_csv(input_file, output_dir, split_by='Property'):
    """
    Split a CSV file by the provided column and create separate CSV files for each unique property value.
    
    Args:
        input_file (str): Path to the input CSV file.
        output_dir (str): Directory where the output CSV files will be created.
        
    Returns:
        None
    """

    # Get the list of all rows in the CSV
    with open(input_file, 'r') as csvfile:
        reader = csv.DictReader(csvfile)
        rows = list(reader)

    # Sort the rows by the provided column
    rows.sort(key=lambda x: x[split_by])

    # Group the sorted rows by the provided column
    grouped_rows = groupby(rows, key=lambda x: x[split_by])

    # Create a new CSV file for each unique property value
    for prop, group in grouped_rows:
        output_file = os.path.join(output_dir, f'split_{prop}.csv')
        
        with open(output_file, 'w', newline='') as out_csvfile:
            writer = csv.DictWriter(out_csvfile, fieldnames=rows[0].keys())
            writer.writeheader()
            
            for row in group:
                writer.writerow(row)

# Example usage:
input_file_path = r"C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/More.csv"
output_dir = r"C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/temp_csvs/split_csvs_MORE/"

split_csv(input_file_path, output_dir)