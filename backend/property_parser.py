#This file is for functions that will parse an entire, single, property.
from helpers.LLM_Handler import LLM_Handler, openAIModels, geminiModels
from helpers.Document_Processing import Doc_Processor
from typing import List, Dict
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import json
from google.genai import types
import re

#llm tools

meal_basis_types_Assumed = ["RO - Room Only", "B&B - Bed & Breakfast", "HB - Half Board", "FB - Full Board", "AI - All-Inclusive","N/A - None Specified"]
meal_basis_types_confirmed = {"FI - Fully Inclusive":"breakfast, lunch, dinner, drinks, and bed included", 
                              "FI and Activities":"breakfast, lunch, dinner, drinks, bed and free activities included",
                              "FB - Full Board":"breakfast, lunch, dinner, and bed, but no drinks", 
                              "B&B - Bed & Breakfast": "Bed and breakfast", 
                              "RO - Room Only": "Room only, no cooking facilities",
                              "SC - Self Catering": "Room with facilities to self-cater",
                              "HF - Half Board with Activities": "Some included meals, and free activities included with the stay",
                              "N/A - None Specified": "The document doesnt give enough or any information about the meal basis."
                              }

class Season(BaseModel):
    name: str = Field(description="The name of the season (e.g., 'Summer')")
    start_date: str = Field(description="The start date of the season in ISO YYYY-MM-DD format")
    end_date: str = Field(description="The end date of the season in ISO YYYY-MM-DD format")

def split_seasons(seasons: List[Season]) -> List[List[str]]:
    """
    Identifies and splits overlapping seasons into non-overlapping periods with combined names.

    Args:
        seasons: A list of seasons, where each season is a Season object or dictionary with fields {name: string, start_date: string, end_date: string}, with dates in ISO YYYY-MM-DD format 

    Returns:
        A list of non-overlapping periods, where each period is represented as a list with:
            - Combined name (str): The name of the period, which may include combined season names.
            - start_date (str): The start date of the period in YYYY-MM-DD format.
            - end_date (str): The end date of the period in YYYY-MM-DD format.
    """
    def parse_date(date_str):
        return datetime.strptime(date_str, "%Y-%m-%d")

    def format_date(date_obj):
        return date_obj.strftime("%Y-%m-%d")

    if not seasons:
        return []

    # Check if input is already split (contains combined names with " - ")
    # If so, return as-is to prevent double processing
    already_split = any(
        (" - " in (season.get("name", "") if isinstance(season, dict) else getattr(season, "name", "")))
        for season in seasons
    )

    if already_split:
        # Convert to expected output format if needed
        result = []
        for season in seasons:
            if isinstance(season, dict):
                name = season["name"]
                start_date = season["start_date"]
                end_date = season["end_date"]
            else:
                name = season.name
                start_date = season.start_date
                end_date = season.end_date
            result.append([name, start_date, end_date])
        return result

    # Create timeline events for sweep line algorithm
    events = []
    season_set = set()

    for season in seasons:
        # Handle both Season objects and dictionaries
        if isinstance(season, dict):
            name = season["name"]
            start_date = season["start_date"]
            end_date = season["end_date"]
        else:
            name = season.name
            start_date = season.start_date
            end_date = season.end_date

        season_dates = (start_date, end_date)
        if season_dates in season_set:
            return f"Error - duplicate seasons with start date {start_date} and end date {end_date}"
        season_set.add(season_dates)

        parsed_start = parse_date(start_date)
        parsed_end = parse_date(end_date)

        if parsed_start > parsed_end:
            return f"Error - in season {name}, start date {start_date} is after end date {end_date}"

        # Use inclusive end dates for the algorithm
        events.append((parsed_start, "start", name))
        events.append((parsed_end, "end", name))

    # Sort events by date, then by type (start before end on same date)
    events.sort(key=lambda x: (x[0], x[1] == "end"))

    active_seasons = set()
    result = []
    current_start = None

    for date, event_type, season_name in events:
        # If we have active seasons, close the current period
        if active_seasons and current_start is not None:
            # Create period name from active seasons
            combined_name = " - ".join(sorted(active_seasons))

            # End date is the day before this event (inclusive)
            end_date = date - timedelta(days=1) if event_type == "start" else date

            result.append([combined_name, format_date(current_start), format_date(end_date)])

        # Update active seasons
        if event_type == "start":
            active_seasons.add(season_name)
        else:  # event_type == "end"
            active_seasons.discard(season_name)

        # Set new period start if we have active seasons
        if active_seasons:
            current_start = date if event_type == "start" else date + timedelta(days=1)
        else:
            current_start = None

    return result

def is_within_range(validity_start: str, validity_end: str, dates: list[str]) -> list[str] | str:
        """
        Identifies any dates that are not within the valid period.

        Args:
            validity_start : The starting date for the period of validity, in ISO YYYY-MM-DD format.
            validity_end : The end date for the period of validity, in ISO YYYY-MM-DD format.
            dates : A list of dates to validate, where each date is in ISO YYYY-MM-DD format.

        Returns:
        --------
            A list of dates that are not within the valid period. Each date is in YYYY-MM-DD format.
        """
        # Convert the validity_start and validity_end strings to datetime objects
        try:
            start_date = datetime.strptime(validity_start, '%Y-%m-%d')
            end_date = datetime.strptime(validity_end, '%Y-%m-%d')
        
            
            invalid_dates = []
            
            for date_str in dates:
                # Convert each date string to a datetime object
                date = datetime.strptime(date_str, '%Y-%m-%d')
                
                # Check if the date is outside the validity range
                if date < start_date or date > end_date:
                    invalid_dates.append(date_str)
            
            if not invalid_dates:
                return "All dates are valid."
            else:
                return f"The following dates are not valid: {', '.join(invalid_dates)}"
            
        except ValueError:
            return f"Invalid date format. Please use YYYY-MM-DD."

class property_parser:

    def __init__(self,doc,property_name, model="gpt-4o-mini"):
        """
        Initialize the document processor with the specified model.

        Args:
            doc (str): The document to check.
            property_name (str): The property name to validate.
            model (str): The model to use for processing. Default is "gpt-4o-mini".
        """
        if not (model in openAIModels or model in geminiModels):
            raise Exception(f"Error - unknown model: {model}")
        self.llm_handler = LLM_Handler(model)
        self.document_processor = Doc_Processor()
        self.doc = doc
        self.property_name = property_name


    

    @staticmethod
    def merge_date_ranges(date_ranges):
        """
        Merge overlapping or continuous date ranges.

        Args:
            date_ranges (list): List of tuples in the format (start_date, end_date).

        Returns:
            list: List of merged date ranges.
        """
        if not date_ranges:
            return []

        # Sort the date ranges by start date
        date_ranges.sort(key=lambda x: x[0])

        merged_ranges = []
        current_start, current_end = date_ranges[0]

        # may need to convert string to date
        if isinstance(current_start, str):
            try:
                current_start = datetime.strptime(current_start, '%Y-%m-%d')
            except:
                current_start = datetime.strptime("0001-01-01", '%Y-%m-%d')
        if isinstance(current_end, str):
            try:
                current_end = datetime.strptime(current_end, '%Y-%m-%d')
            except:
                    current_end = datetime.strptime("0001-01-01", '%Y-%m-%d')


        for start_date, end_date in date_ranges[1:]:
            # Check if the current range overlaps or is continuous with the next range
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d')
            
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d')
            
            
            if start_date <= (current_end + timedelta(days=1)):
                # Merge the ranges by updating the current_end
                current_end = max(current_end, end_date)
            else:
                # No overlap or continuity, add the current range to merged_ranges
                merged_ranges.append((current_start, current_end))
                current_start, current_end = start_date, end_date

        # Add the last range
        merged_ranges.append((current_start, current_end))

        return merged_ranges

    def validate_property(self):
        """
        Validate a property in the document.


        Returns:
            bool: True if the property is valid, False otherwise.
        """
        prompt = f"""In the following document which should contain accommodation information, is the property "{self.property_name}" valid, and does it have sufficient information present to determine the price of staying at the property at a specific season? 
        You may return reasoning but the final line of your response should consist of 'CONCLUSION: VALID' or 'CONCLUSION: INVALID'."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        if isinstance(response, str):
            if 'CONCLUSION: VALID' in response.upper():
                return True
            if 'CONCLUSION: INVALID' in response.upper():
                return False
        print(f"invalid response for {self.property_name}", response)
        return False
        

    def get_room_types(self):
        """
        Get the room types available in a property.

        Returns:
            list: List of room types.
        """
        prompt = f"""In the following document which should contain accommodation information, what are the names of the types of rooms or accommodations available in the property "{self.property_name}"?
        For example, this may be "Standard", "Suite", "Superior", "Deluxe" etc. at a hotel, or may be more specific, for example "CAMPSITE CK6P" or "FAMILY COTTAGE FA6C".
        Each should contain info such as pricing and capacity for that specific room or accommodation type. Do not consider these separate rooms - for instance a "Luxury Room" may have different prices depending on number of people, but do not include these as separate rooms - only as "Luxury Room".
        Return **ONLY** each room type that is **EXPLICITLY** available at {self.property_name}, with each on a new line, withou any preamble or added descriptions.
        If no relevant data is found return only NONE"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")
        return [resp.strip() for resp in resplist if resp.strip()]

    def get_meal_types(self, types=meal_basis_types_confirmed):
        """
        Get the meal types available in a property.

        Returns:
            list: List of meal types.
        """
        prompt = f"""In the following document which should contain accommodation information, what meal types are available in the property "{self.property_name}"? 
        Remember!!:
            - A communal kitchen, shared kitchen, or kitchenette typically indicates a self-catering or self-service arrangement.
            
        Select the best match from the following list (which includes explanations of each): {types}
        Return only each meal type on a new line."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")
        resplist = [resp.strip() for resp in resplist if resp.strip()]
        #validate that each element of resplist is contained within types
        listToReturn = []
        for element in resplist:
            for allowed_meal_type in types:
                if str(element).upper() in str(allowed_meal_type).upper():
                    listToReturn.append(allowed_meal_type)
                    break

        return listToReturn
    
    def get_headings(self):
        """
        Get the headings for a table in the document.


        Returns:
            list: List of headings with corresponding row values.
        """
        prompt = f"""In the following document which should contain accommodation information in a pdf table read as a string, what are the headings for the table "{self.property_name}", with a corresponding row value from the first row.
        You may assume that in this string there are multiple rows in the table.
        Return only each heading name + the example value on a new line."""

        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")
        return [resp.strip() for resp in resplist if resp.strip()]
    
    
    def get_periods_at_property(self, periods):
        """
        Get the periods used to define seasonal price ranges at a specific property.

        Args:
            periods (list): List of overall periods.

        Returns:
            list: List of periods at the property.
        """
        prompt = f"""In the following document which should contain accommodation information, what periods are used to define seasonal price ranges at the property "{self.property_name}"?
        Primarily consider the following seasons:"""
        for period in periods: prompt += f"{period}\n"   
        prompt += f"Return only each period name on a new line."
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        resplist = response.split("\n")
        return [resp.strip() for resp in resplist if resp.strip()]
    
    def has_sto(self, period, start, end):
        """
        Check if a specific property during a specific period has a distinction between STO Rate and Published Rate.

        Args:
            period (str): The period name.
            start (str): The start date of the period.
            end (str): The end date of the period.

        Returns:
            bool: True if the distinction exists, False otherwise.
        """
        prompt = f"""In the following document which should contain accommodation information, is there a distinction between a "STO Rate" and a "Published Rate" at "{self.property_name}" during {period} ({start}-{end})?
        Return TRUE if there is an explicitly mentioned STO Rate and Published rate, or FALSE if there is not."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.upper()=="TRUE"
    
    def get_period_Length(self, period, validity_period, num_attempts=3):
        """
        Get the date ranges for a specific period at a property.

        Args:
            doc (str): The document to check.
            property_name (str): The property name.
            period (str): The period name.
            validity_period (list): List containing the start date and end date of the overarching period.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            list: List of date ranges for the period at the property.
        """
        prompt = f"""In the following document which should contain accommodation information, when does the {period} period start and end at "{self.property_name}"? Bear in mind that the period may consist of multiple date ranges.
        After stating what information is present regarding the {period} period in the document in general, and then discussing whether there is any specific statements regarding {period} for {self.property_name}, return a YAML response with the following data:
        Dates: A list containing only the date ranges for {period} at {self.property_name}. Each date range should be an array of format [start_date, end_date]. Format dates in ISO format as follows: YYYY-MM-DD.  If you cannot find a date, use a default date of 0001-01-01"""
        if len(validity_period) > 1: 
            prompt += f"""\nAll dates should be contained within the range: {validity_period[0]} - {validity_period[1]}.\n You may use the is_within_range function to verify that your answers are within this range, if needed."""
        
        gptTools = [{
        "type": "function",
        "function": {
            "name": "is_within_range",
            "description": """Identifies any dates that are not within the valid period. You do not need to specify the validity period.""",
            "parameters": {
                "type": "object",
                "properties": {
                    "dates": {
                        "type": "array",
                        "description": "A list of dates to validate",
                        "items": {
                            "type": "string",
                            "description": "A date in ISO YYYY-MM-DD format."
                        }
                    }
                },
                "required": ["dates"],
                "additionalProperties": False
            }
            }
        }]

        geminiTools = [is_within_range]

        def tool_call_function(name, args):
            if name == "is_within_range":
                dates = args["dates"]
                #print(seasons)
                result = is_within_range(validity_period[0],validity_period[1],dates)
                
                return result
            else:
                print("Incorrect tool use:",name)
                return "Incorrect tool use."

        attempt = 0
        while attempt < num_attempts:
            final_response = False
            if len(validity_period) > 1 and gptTools and geminiTools:
                response = self.llm_handler.sendMessageToLLM(self.doc, prompt,GptTools=gptTools, GeminiTools=geminiTools, tool_function=tool_call_function)
                if (len(response) == 2):
                    msg, messages = response
                else:
                    final_response = True
            else:
                response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
                final_response = True

            while not final_response:
                if isinstance(response, str):
                    final_response = True
                    break

                provider = self.llm_handler.get_model_provider(self.llm_handler.model)
                if provider=="openai":
                    if msg.tool_calls == None or len(msg.tool_calls)==0:
                        response = msg.content
                        final_response = True
                        break
                    messages.append(msg)
                    for tool_call in msg.tool_calls:
                        name = tool_call.function.name
                        args = json.loads(tool_call.function.arguments)
                        print("Using tool:",name, args)
                        result = tool_call_function(name, args)
         
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": str(result)
                        })
                elif provider=="gemini":
                    for part in msg.candidates[0].content.parts:
                        if part.function_call:
                            function_call = part.function_call
                            result = tool_call_function(function_call.name, function_call.args)
                            
                            function_response_part = types.Part.from_function_response(
                                name=function_call.name,
                                response={"result": result},
                            )
                            
                            messages.append(types.Content(role="model", parts=[types.Part(function_call=function_call)]))
                            messages.append(types.Content(role="user", parts=[function_response_part]))
            
                response = self.llm_handler.sendMessagesToLLM(messages, prompt, GptTools=gptTools, GeminiTools=geminiTools)
                if (len(response) == 2):
                    msg, messages = response
                else:
                    final_response = True
        
            try:
                parsed_response = self.llm_handler.parse_yaml(response)
                return parsed_response["Dates"]
            except:
                attempt += 1
            
        return {"error": "Failed to parse json response correcting element after multiple attempts."} 

    def check_periods(self, periods, validity_period, num_attempts = 3):
        """
        Check the validity of periods and their date ranges at a specific property.

        Args:
            periods (list): List of periods with date ranges.
            validity_period (list): List containing the start date and end date of the overarching period.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            list: List of non-overlapping periods/seasons.
        """
        prompt = f"""You are checking the work of a junior data entry worker for a tourism company. The worker was given a document section that should contain accommodation information, and was asked to list each period used to define seasonal price ranges at the property "{self.property_name}".

The following periods were identified:\n"""
        for period in periods:
            if isinstance(period, str):
                prompt += f"- {period}\n"
            elif isinstance(period, list) and len(period) >= 3:
                prompt += f"- {period[0]}: {period[1]} to {period[2]}\n"
            elif isinstance(period, tuple) and len(period) >= 3:
                prompt += f"- {period[0]}: {period[1]} to {period[2]}\n"

        if isinstance(validity_period, list) and len(validity_period) > 1:
            prompt += f"""\nIMPORTANT: All dates must be contained within the valid range: {validity_period[0]} to {validity_period[1]}\n"""

        prompt += f"""
TASK: Carefully review these periods against the document content and:

1. VALIDATE each period name matches what's described in the document for "{self.property_name}"
2. VERIFY all date ranges are accurate according to the document
3. CHECK that all dates fall within the validity period specified above
4. IDENTIFY any overlapping periods that need to be split using the split_seasons tool
5. DETECT any missing periods that should be included based on the document
6. CORRECT any date format issues (documents use international DD/MM/YYYY format, expect Southern Hemisphere seasons)

IMPORTANT NOTES:
- Documents are from South Africa (Southern Hemisphere seasons)
- Dates should be in international format (DD/MM/YYYY in source, convert to ISO YYYY-MM-DD)
- If end date appears before start date, this may indicate month/day swap
- A season may consist of multiple separate date ranges
- Use the split_seasons tool if you detect overlapping periods

REQUIRED OUTPUT FORMAT:
After explaining your analysis and any corrections made, return a JSON response with:
{{
  "PERIODS": [
    ["season_name", "YYYY-MM-DD", "YYYY-MM-DD"],
    ["season_name", "YYYY-MM-DD", "YYYY-MM-DD"]
  ]
}}

Each period must be a 3-element array: [name, start_date, end_date] with dates in ISO format.
Ensure NO overlapping date ranges in your final result."""
        gptTools = [{
        "type": "function",
        "function": {
            "name": "split_seasons",
            "description": """Identifies and splits overlapping seasons into non-overlapping periods with combined names. 
            Example input:[
                ["Summer", "2025-06-01", "2025-08-31"],
                ["Peak", "2025-07-15", "2025-08-15"],
                ["Fall", "2025-09-01", "2025-11-30"]
            ]
            Example output: [
                ["Summer", "2025-06-01", "2025-07-14"],
                ["Peak - Summer", "2025-07-15", "2025-08-14"],
                ["Summer", "2025-08-15", "2025-08-30"],
                ["Fall", "2025-09-01", "2025-11-29"]
            ]""",
            "parameters": {
                "type": "object",
                "properties": {
                    "seasons": {
                        "type": "array",
                        "description": "A list of seasons, where each season is represented as an object with name, start_date, and end_date.",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "string",
                                    "description": "The name of the season (e.g., 'Summer')."
                                },
                                "start_date": {
                                    "type": "string",
                                    "description": "The start date of the season in ISO YYYY-MM-DD format."
                                },
                                "end_date": {
                                    "type": "string",
                                    "description": "The end date of the season in ISO YYYY-MM-DD format."
                                }
                            },
                            "required": ["name", "start_date", "end_date"]
                        }
                    }
                },
                "required": ["seasons"],
                "additionalProperties": False
            }
            }
        }]

        def tool_call_function(name, args):
            if name == "split_seasons":
                seasons = args["seasons"]
                #print(seasons)
                result = split_seasons(seasons)   #might not need anything more than this? 
                
                return result
            else:
                print("Incorrect tool use:",name)
                return "Incorrect tool use."


        geminiTools = [split_seasons]
        attempt = 0
        while attempt < num_attempts:
            final_response = False
            if len(validity_period) > 1 and gptTools and geminiTools:
                response = self.llm_handler.sendMessageToLLM(self.doc,prompt,GptTools=gptTools, GeminiTools=geminiTools, tool_function = tool_call_function)
                if (len(response) == 2):
                    msg, messages = response
                else:
                    if isinstance(response,str) and response.lower().startswith("error"):
                        print("retrying due to error:", response)
                        attempt += 1
                        continue
                    final_response = True
            else:
                response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
                final_response = True
            while not final_response:
                if msg.tool_calls == None or len(msg.tool_calls)==0:
                    response = msg.content
                    final_response = True
                    break
                messages.append({
                            "role": "assistant",
                            "content": str(msg.content),
                            "tool_calls": msg.tool_calls
                        })
                for tool_call in msg.tool_calls:
                    name = tool_call.function.name
                    args = json.loads(tool_call.function.arguments)
                    result = tool_call_function(name, args)
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": str(result)
                    })

                  
            
                response = self.llm_handler.sendMessagesToLLM(messages, prompt, GptTools=gptTools, GeminiTools=geminiTools)
                if (len(response) == 2):
                    msg, messages = response
                else:
                    final_response = True
        
        
            try:
                parsed_response = self.llm_handler.parse_json(response)
                periods = parsed_response["PERIODS"]

                # Final validation: run through split_seasons to ensure no overlapping periods
                if periods and isinstance(periods, list):
                    # Convert periods to the format expected by split_seasons
                    seasons_for_validation = []
                    for period in periods:
                        if isinstance(period, list) and len(period) >= 3:
                            # Validate date format before adding to validation list
                            try:
                                # Test if dates are in correct format by parsing them
                                from datetime import datetime
                                datetime.strptime(period[1], "%Y-%m-%d")
                                datetime.strptime(period[2], "%Y-%m-%d")

                                seasons_for_validation.append({
                                    "name": str(period[0]),
                                    "start_date": str(period[1]),
                                    "end_date": str(period[2])
                                })
                            except ValueError as date_error:
                                print(f"Warning: Invalid date format in period {period}: {date_error}")
                                # Skip this period if dates are invalid
                                continue

                    if seasons_for_validation:
                        try:
                            # Use split_seasons to ensure no overlaps
                            validated_periods = split_seasons(seasons_for_validation)

                            # Check if split_seasons returned an error
                            if isinstance(validated_periods, str) and "Error" in validated_periods:
                                print(f"Warning: split_seasons validation failed: {validated_periods}")
                                return periods  # Return original periods if validation fails

                            return validated_periods
                        except Exception as split_error:
                            print(f"Warning: split_seasons function failed: {split_error}")
                            return periods  # Return original periods if split_seasons fails

                return periods

            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {str(e)}")
                attempt += 1

        return {"error": "Failed to parse json response correcting element after multiple attempts."}
    
    def get_includes(self):
        prompt = f"""You are a travel agent, helping me make sense of some documentation relating to accommodation and tourist experiences.
        Based on the following document, what are the INCLUSIONS at '{self.property_name}'? Look specifically for the inclusions/includes keyword if it exists, as this will contain the bulk of the required information.
        Remember to return the included elements for **ONLY** '{self.property_name}' (unless it is included for all properties - then also include it), listed briefly and separated by commas, NO repetition.
        
        Do NOT list room or accommodation types (like bungalow names, cottage numbers, etc.) — only list what the stay includes.
        For example:
        "Accommodation, three meals daily, soft drinks, house wines, local brand spirits and beers, teas and coffees, refreshments on game drives, laundry, safari activities, emergency medical evacuation cover, VAT, transfers to and from the lodge airstrip and park fees, Guides' Journal with illustrated wildlife and vegetation checklist and an area map with illustrated Star Birds checklist, One complimentary pair of SWAROVSKI OPTIK Companion binoculars per room to share during your stay."
        If no INCLUSIONS section is found, return something similar to the above example using whatever information is in the document. If no relevant information is found, but it is an accommodation, simply return "Accommodation"."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip()
    
    def get_excludes(self):
        prompt = f"""You are a travel agent, helping me make sense of some documentation relating to accommodation and tourist experiences.
        Based on the following document, what are the EXCLUSIONS at '{self.property_name}'?
        Look specifically for the exclusions/excludes keyword if it exists, , as this will contain the bulk of the required information.
        Return each excluded element, listed briefly and separated by commas.
        For example:
        "Telephone calls, Safari Shop purchases, gratuities, all items of a personal nature, champagne, cognacs, fine wines, premium brand spirits, cigars, landing fees."

        If no EXCLUSIONS section is found, return something similar to the above example using whatever information is in the document. If no relevant information is found, simply return "Unknown"."""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        return response.strip()
    
    def get_levy(self):
        """
        Get the different levies mentioned in the document.

        Returns:
        list: List of the levies identified.
            
        """
        prompt = f"""In the following document, there may be various levies mentioned, such as tourism levies (TOMSA), community levies, or similar. 
        Identify and extract each type of levy for the property "{self.property_name}" along with its percentages or cost.

        IGNORE:
        - Any percentages that do not correspond to a LEVY.
        - The following are NOT levies and should be excluded: taxes (e.g., VAT, IVA), discounts, surcharges (e.g., holiday fees), and cost rates (e.g., STO, wholesale pricing).

        Return a YAML response with the following data:
        TYPE: The type of levy.
        PERCENTAGE: The percentage of the levy (if applicable, otherwise 0).
        COST: The rand value of the levy (if applicable, otherwise 0, There may be something like age/date ranges, include all the ranges).
        Remember to only return LEVY data relating to "{self.property_name}", and not any other potentially similar accommodations at different properties. 
        If no LEVY data is found, return the word 'NONE'"""
        
        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(self.doc,prompt)
    
        
    def general_child_policy(self):
        prompt = f"""You are a travel agent, helping me make sense of some documentation relating to accommodation and tourist experiences.
        Based on the following document, what age ranges are used to define children at '{self.property_name}'? 
        The names of the categories need not be returned, only the age ranges, separated by commas.
        For example, if children are categorised into infants aged 0-2, toddlers age 3-5 and children 6-16, then return the following:
        0-2, 3-5, 6-16.
        Return ONLY the ranges given, don't create your own to fill up the gap to 18.
        If no age ranges are defined, assume the default age range and so return the following: 0-18"""
        response = self.llm_handler.sendMessageToLLM(self.doc,prompt)
        # Extract age ranges from the response
        age_ranges = []
        matches = re.findall(r'(\d+)-(\d+)', response)
        for match in matches:
            age_ranges.append((int(match[0]), int(match[1])))
        return age_ranges

    def has_distinct_week_rates(self, period, start, end):
        prompt = f"""In the following document which should contain accommodation information, does "{self.property_name}" have distinct rates for different days of the week, or different kinds of stay, during {period} ({start}-{end})?
        Look for mention of "week rates", "weekday rates", "weekend rates", or similar terms that indicate different pricing for different days of the week, and "long stay" or equivalent to indicate a different pricing for longer stays.
        If you are able to find distinct rates for these different periods, return a json formatted list of these different periods, including a "Normal" period if logical. For example, ['Weekdays', 'Weekends'] or ['Normal','Long Stay'] or ['Weekdays', 'Weekends', 'Long Stay'].
        If you are unable to find distinct rates for different periods, return an empty list [] or ['Normal']."""
        return self.llm_handler.sendMessageToLLM_Expecting_JSON_Response(self.doc,prompt)
        
    
if __name__=="__main__":
    with open("backend/temp/oneflow-7385867_Full Document.txt", "r") as f:
        doc = f.read()
    parser = property_parser(doc,"Radisson Blu Hotel, Waterfront", "gpt-4.1-mini")
    parser.check_periods(["Season 1"],["2025-01-01","2026-01-01"])