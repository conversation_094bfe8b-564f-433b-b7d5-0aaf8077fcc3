import csv
import os
from helpers.LLM_Handler import <PERSON><PERSON>_Handler
from helpers.Document_Processing import Doc_Processor
from general_document_parser import general_document_parser
from property_parser import property_parser
from room_type_parser import room_type_parser
import re

# List of service providers
service_providers = [
    "&Beyond", "Africa In Focus", "African Anthology", "African Safari Collective",
    "African Synergy", "Aha", "Amakhala", "Caleo Foundation", "De Hoop Collection",
    "Desert & Delta Safaris", "Extraordinary Marketing", "Kariega", "Karongwe Portfolio",
    "Kim Beyers", "Kwandwe Private Reserve", "Leeu Collection", "Legacy Hotels",
    "Lion Roars", "Liz McGrath", "Marriott Hotels", "Minor Hotels", "MORE",
    "Natural Selection", "Newmark", "One & Only Cape Town", "Radisson",
    "Rare Earth Retreats", "Red Carnation", "Sabi Sabi", "SANPARKS", "Seasons in Africa",
    "Shamwari", "Southern Spoor", "Southern Sun", "Sun International",
    "Unlimited Destinations", "Village & Life",
]


def get_doc_data(pdfname, pdfstr, target_property=None, model="gemini-2.0-flash", user_controlled=False, path=None):
    """
    Extract data from hotel contract documents and store in dictionary format.
    
    Args:
        pdfname (str): Name of the PDF file
        pdfstr (str): Content of the PDF file
        target_property (str, optional): Specific property to extract data for
        model (str, optional): Model to use for extraction
        
    Returns:
        str: Path to the generated CSV file
    """
    doc_parser = general_document_parser(pdfstr, model=model)
    extracted_data = []

    # DOCUMENT LEVEL VALIDATION
    doc_is_valid = doc_parser.get_valid()
    print(f"Document is valid: {doc_is_valid}")
    
    if not doc_is_valid:
        print("Document is not valid. Exiting.")
        return None

    # DOCUMENT LEVEL DATA EXTRACTION
    overarching_period = doc_parser.get_overarching_period()
    print(f'Validity period: {overarching_period}')
    
    overall_periods = doc_parser.get_periods()
    print(f'Overall periods: {overall_periods}')
    
    supplier = doc_parser.get_supplier_name(service_providers)
    print(f'Supplier: {supplier}')
    
    properties = doc_parser.get_property_names()
    print(f"Properties: {properties}")
    
    # Filter for specific property if requested
    if target_property:
        properties = [p for p in properties if p.strip().lower() == target_property.strip().lower()]
        print(f"Filtered properties: {properties}")
        if not properties:
            print(f"Property '{target_property}' not found in the document.")
            return None

    # PROPERTY LEVEL DATA EXTRACTION
    for property_name in properties:
        if user_controlled:
            user_input = input(f"Do you want to extract data for property '{property_name}'? (y/n): ").strip().lower()
            if user_input != 'y':
                print(f"Skipping property '{property_name}'.")
                continue
        prop_parser = property_parser(pdfstr, property_name, model=model)
        prop_is_valid = prop_parser.validate_property()
        
        if not prop_is_valid:
            print(f"Property '{property_name}' is invalid! Skipping to next.")
            continue
        
        # Extract periods for this property
        periods = prop_parser.get_periods_at_property(overall_periods)
        print(f"Initial identified periods: {periods}")
        
        periods_with_times = []
        for period in periods:
            times = prop_parser.get_period_Length(period, overarching_period)
            print(times)
            # Handle multiple date ranges for a period
            merged_date_ranges = prop_parser.merge_date_ranges(times)
            for time in merged_date_ranges:
                period_with_time = (period, time[0], time[1])
                if period_with_time not in periods_with_times:  # Avoid duplicates
                    periods_with_times.append(period_with_time)

        checked_periods = prop_parser.check_periods(periods_with_times, overarching_period)
        print(f"Property periods: {checked_periods}")
        
        # Extract property-level information
        meal_types = prop_parser.get_meal_types()
        print(f"Meal types: {meal_types}")
        
        includes = prop_parser.get_includes()
        print(f"Includes: {includes}")
        
        excludes = prop_parser.get_excludes()
        print(f"Excludes: {excludes}")
        
        room_types = prop_parser.get_room_types()
        print(f"Room types: {room_types}")
        
        child_ranges = prop_parser.general_child_policy()
        print(f"Child ranges: {child_ranges}")
        
        # ROOM LEVEL DATA EXTRACTION
        for room_type in room_types:
            room_parser = room_type_parser(pdfstr, property_name, room_type, model=model)
            
            # Extract room-level information
            meal_type = room_parser.get_meal_type(meal_types)
            print(f"Meal type: {meal_type}")
            
            min_stay = room_parser.min_night_stay()
            print(f"Minimum stay: {min_stay}")
            
            # Extract room capacity information
            room_capacity = room_parser.get_room_capacity()
            max_adults = room_capacity.get("MAX_ADULTS", 0)
            max_children = room_capacity.get("MAX_CHILDREN", 0)
            capacity_sum = int(max_adults) + int(max_children)
            
            # Extract cancellation policy
            cancellation_policy = room_parser.cancellation_policy()
            print(f"Cancellation policy: {cancellation_policy}")
            policy_data = cancellation_policy.get("POLICIES", [])
            
            # Extract pay/stay offers
            pay_stay = room_parser.get_stay_type()
            print(f"Pay/Stay offers: {pay_stay}")
            pay_stay_data = pay_stay.get("OFFERS", [])
            
            # For each period, create a data record
            for period in checked_periods:
                period_name = period[0]
                validity_from = period[1]
                validity_to = period[2]
                
                # Create a dictionary for this record
                record = {
                    "Supplier": supplier,
                    "Property": property_name,
                    "Room type": room_type,
                    "Meal basis": meal_type,
                    "DATE_FROM": validity_from,
                    "DATE_TO": validity_to,
                    "Min stay": min_stay,
                    "Includes": includes,
                    "Excludes": excludes,
                    "Max adults": max_adults,
                    "Max Children": max_children,
                    "Max A+C": capacity_sum,
                    "Period": period_name
                }
                #"Double room rate","Single room rate","Triple room rate","Quad room rate","Child rate 1","Child rate 2","Infant rate"
                # Get single room rate information
                single_room_info = room_parser.get_single_room_rate(period)
                record["single_rate_pre_extra"] = sum_room_rates(single_room_info) if single_room_info else 0
                record["single_extra"] = sum([fixCurrency(str(fee)) for fee in single_room_info.get("ADDITIONAL_FEES", [])]) if single_room_info else 0
                record["Single room rate"] = record["single_rate_pre_extra"] + record["single_extra"]

                # Get double rate if room capacity allows
                if max_adults >= 2:
                    double_room_info = room_parser.get_double_room_rate(period)
                    record["double_rate_pre_extra"] = sum_room_rates(double_room_info) if double_room_info else 0
                    record["double_extra"] = sum([fixCurrency(str(fee)) for fee in double_room_info.get("ADDITIONAL_FEES", [])]) if double_room_info else 0
                    record["Double room rate"] = record["double_rate_pre_extra"] + record["double_extra"]

                # Get triple rate if room capacity allows
                if max_adults >= 3:
                    triple_room_info = room_parser.get_triple_room_rate(period)
                    record["triple_rate_pre_extra"] = sum_room_rates(triple_room_info) if triple_room_info else 0
                    record["triple_extra"] = sum([fixCurrency(str(fee)) for fee in triple_room_info.get("ADDITIONAL_FEES", [])]) if triple_room_info else 0
                    record["Triple room rate"] = record["triple_rate_pre_extra"] + record["triple_extra"]

                # Get quad rate if room capacity allows
                if max_adults >= 4:
                    quad_room_info = room_parser.get_quad_room_rate(period)
                    record["quad_rate_pre_extra"] = sum_room_rates(quad_room_info) if quad_room_info else 0
                    record["quad_extra"] = sum([fixCurrency(str(fee)) for fee in quad_room_info.get("ADDITIONAL_FEES", [])]) if quad_room_info else 0
                    record["Quad room rate"] = record["quad_rate_pre_extra"] + record["quad_extra"]

                # Get child rates if room capacity allows
                if max_children >= 1:
                    record["Child rate 1"] = room_parser.get_child_rate(period, supplier)
                    if max_children >= 2:
                        record["Child rate 2"] = room_parser.get_child_2_rate(period, supplier)
                    else:
                        record["Child rate 2"] = -1
                else:
                    record["Child rate 1"] = -1
                    record["Child rate 2"] = -1

                # Get infant rate
                infant_rate = room_parser.get_infant_rate(period, supplier)
                if infant_rate and infant_rate > 0:
                    record["Infant rate"] = infant_rate

                # Add child ranges to the record
                for i, (min_age, max_age) in enumerate(child_ranges, 1):
                    record[f"Child {i} From age"] = min_age
                    record[f"Child {i} To age"] = max_age
                
                # Add cancellation policies to the record
                for i, policy in enumerate(policy_data, 1):
                    if isinstance(policy, dict):
                        record[f"Cancellation Policy from days {i}"] = policy.get("START_DAY", 0)
                        record[f"Cancellation fee % {i}"] = policy.get("CANCELLATION_FEE", 0)
                    else:
                        print(f"ERROR: policy is not dict: {policy}")
                        record[f"Cancellation Policy from days {i}"] = 0
                        record[f"Cancellation fee % {i}"] = 0
                
                # Add pay/stay offers to the record
                for i, offer in enumerate(pay_stay_data, 1):
                    if offer.get("PAY_STAY_DAYS") or offer.get("PAY_STAY_FREE_NIGHTS"):
                        record[f"Pay stay days {i}"] = offer.get("PAY_STAY_DAYS", 0)
                        record[f"Pay stay free nights {i}"] = offer.get("PAY_STAY_FREE_NIGHTS", 0)
                
                extracted_data.append(record)
    
    model_provider = LLM_Handler.get_model_provider(model)
    return write_data_to_csv(extracted_data, pdfname, model_provider, path)

def sum_room_rates(yamlresponse):
    """
    Sum the room rates from a YAML response.

    Args:
        yamlresponse (dict): The YAML response containing room rates.

    Returns:
        float: The sum of the room rates.
    """
    result = fixCurrency(str(yamlresponse["BASE_RATE"])) if yamlresponse["BASE_RATE"] else 0
    if yamlresponse.get("ADDITIONAL_FEES"):
        for fee in yamlresponse["ADDITIONAL_FEES"]:
            result += fixCurrency(str(fee))
    return result
def fixCurrency(input_string):
        """
        Fix the currency format in a string.

        Args:
            input_string (str): The input string containing the currency.

        Returns:
            float: The fixed currency value.
        """
        # Use regex to find all digits and optional decimal points
        number_match = re.search(r'-?\d*\.?\d+', input_string)
        
        if number_match:
            # Extract the matched number
            number_str = number_match.group(0)
        else:
            number_str = "-1"
        
        return float(number_str)

def write_data_to_csv(data, pdfname, model_provider="", path=None):
    """
    Write the processed data to a CSV file.
    
    Args:
        data (list): List of dictionaries containing the extracted data
        pdfname (str): Name of the original PDF file
        
    Returns:
        str: Absolute path to the generated CSV file
    """
    if not data:
        print("No data to write.")
        return None
    
    # Generate output filename
    base_name = os.path.splitext(os.path.basename(pdfname))[0] + f"_{model_provider}_output.csv"
    
    # Get all unique keys from all records to ensure all columns are included
    all_keys = set()
    for record in data:
        all_keys.update(record.keys())
    
    # Define column order with fixed columns first
    fixed_columns = [
        "Supplier", "Property", "Room type", "Meal basis", 
        "DATE_FROM", "DATE_TO", "Min stay", 
        "Includes", "Excludes", "Period"
    ]
    
    # Add child range columns in order
    child_columns = sorted([k for k in all_keys if k.startswith("Child")])
    
    # Add capacity columns
    capacity_columns = ["Max adults", "Max Children", "Max A+C"]
    
    # Add cancellation policy columns in order
    cancellation_columns = sorted([k for k in all_keys if k.startswith("Cancellation")])
    
    # Add pay/stay columns in order
    pay_stay_columns = sorted([k for k in all_keys if k.startswith("Pay stay")])
    
    # Add rate columns
    rate_columns = [
        "Single room rate",
        "Double room rate",
        "Triple room rate",
        "Quad room rate",
        "Child rate 1",
        "Child rate 2",
        "Infant rate"
    ]
    
    # Combine all columns in the desired order
    headers = fixed_columns + child_columns + capacity_columns + cancellation_columns + pay_stay_columns + rate_columns
    
    # Write to CSV
    #create the directory if it doesn't exist
    if path:
        os.makedirs(path, exist_ok=True)
        base_name = os.path.join(path, base_name)
    else:
        # Default path if not provided
        if not os.path.exists("temp_csvs"):
            os.makedirs("temp_csvs", exist_ok=True)     
        base_name = os.path.join("temp_csvs", base_name)

    with open(base_name, mode='w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers, quoting=csv.QUOTE_ALL, extrasaction='ignore')
        writer.writeheader()
        for record in data:
            writer.writerow(record)
    
    absolute_path = os.path.abspath(base_name)
    print(f"Data written to {base_name}")
    return absolute_path


def test_with_sample_data():
    """
    Test the extraction and CSV conversion with sample data.
    This function creates sample data and validates the CSV output.
    """
    # Create sample data
    sample_data = [
        {
            "Supplier": "Test Supplier",
            "Property": "Test Property 1",
            "Room type": "Deluxe Room",
            "Meal basis": "Breakfast",
            "DATE_FROM": "2025-01-01",
            "DATE_TO": "2025-12-31",
            "Min stay": 2,
            "Includes": "WiFi, Parking",
            "Excludes": "Airport Transfer",
            "Max adults": 2,
            "Max Children": 1,
            "Max A+C": 3,
            "Period": "High Season",
            "Child 1 From age": 0,
            "Child 1 To age": 12,
            "Cancellation Policy from days 1": 30,
            "Cancellation fee % 1": 50,
            "Pay stay days 1": 3,
            "Pay stay free nights 1": 1
        },
        {
            "Supplier": "Test Supplier",
            "Property": "Test Property 2",
            "Room type": "Suite",
            "Meal basis": "All Inclusive",
            "DATE_FROM": "2025-01-01",
            "DATE_TO": "2025-06-30",
            "Min stay": 3,
            "Includes": "WiFi, Parking, Spa",
            "Excludes": "None",
            "Max adults": 4,
            "Max Children": 2,
            "Max A+C": 6,
            "Period": "Low Season",
            "Child 1 From age": 0,
            "Child 1 To age": 6,
            "Child 2 From age": 7,
            "Child 2 To age": 12,
            "Cancellation Policy from days 1": 60,
            "Cancellation fee % 1": 25,
            "Cancellation Policy from days 2": 30,
            "Cancellation fee % 2": 50,
            "Pay stay days 1": 4,
            "Pay stay free nights 1": 1,
            "Pay stay days 2": 7,
            "Pay stay free nights 2": 2
        }
    ]
    
    # Write sample data to CSV
    test_file_path = "test_output.csv"
    csv_path = write_data_to_csv(sample_data, test_file_path)
    
    # Validate the output by reading it back
    with open(csv_path, mode='r', newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        rows = list(reader)
        
        # Check if all rows were written correctly
        assert len(rows) == len(sample_data), f"Expected {len(sample_data)} rows, got {len(rows)}"
        
        # Check if all columns were preserved
        for i, row in enumerate(rows):
            for key, value in sample_data[i].items():
                assert key in row, f"Column {key} missing in row {i+1}"
                assert str(row[key]) == str(value), f"Value mismatch for {key} in row {i+1}: expected {value}, got {row[key]}"
    
    print("Validation successful: CSV output matches input data")
    return csv_path


if __name__ == "__main__":
    # Test with sample data
    # test_csv_path = test_with_sample_data()
    # print(f"Test CSV written to: {test_csv_path}")
    
    # Real document processing would be done like this:
    document_processor = Doc_Processor()
    
    pdf_name = "backend/temp_csvs/Raddisson Blu Hotel, Waterfront.csv"
    
    if pdf_name.endswith(".pdf"):
        pdfstr = document_processor.read_pdf_to_string(pdf_name)
    else:
        with open(pdf_name, "r") as f:
            pdfstr = f.read()

    doc_info = get_doc_data(pdf_name, pdfstr, model="gpt-4.1-mini", user_controlled=True)
