#!/usr/bin/env python3
"""
Test script to verify model upgrading functionality.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from helpers.model_utils import upgrade_model_to_level_2
from helpers.LLM_Handler import LLM_Handler

def test_model_upgrade():
    """Test the model upgrade functionality."""

    # Test constants
    DEFAULT_GEMINI_MODEL = "gemini-2.0-flash"
    DEFAULT_GPT_MODEL = "gpt-4o-mini"

    print("Testing model upgrade functionality...")
    print("=" * 50)

    # Test 1: Upgrade Gemini model
    print(f"Test 1: Upgrading {DEFAULT_GEMINI_MODEL}")
    upgraded_gemini = upgrade_model_to_level_2(DEFAULT_GEMINI_MODEL, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)
    expected_gemini = LLM_Handler.latest_medium_gemini_model()
    print(f"  Input: {DEFAULT_GEMINI_MODEL}")
    print(f"  Output: {upgraded_gemini}")
    print(f"  Expected: {expected_gemini}")
    print(f"  ✓ PASS" if upgraded_gemini == expected_gemini else f"  ✗ FAIL")
    print()

    # Test 2: Upgrade GPT model
    print(f"Test 2: Upgrading {DEFAULT_GPT_MODEL}")
    upgraded_gpt = upgrade_model_to_level_2(DEFAULT_GPT_MODEL, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)
    expected_gpt = LLM_Handler.latest_medium_openai_model()
    print(f"  Input: {DEFAULT_GPT_MODEL}")
    print(f"  Output: {upgraded_gpt}")
    print(f"  Expected: {expected_gpt}")
    print(f"  ✓ PASS" if upgraded_gpt == expected_gpt else f"  ✗ FAIL")
    print()

    # Test 3: No upgrade for already level 2 model
    level_2_model = "gemini-2.5-flash"
    print(f"Test 3: No upgrade for level 2 model {level_2_model}")
    no_upgrade = upgrade_model_to_level_2(level_2_model, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)
    print(f"  Input: {level_2_model}")
    print(f"  Output: {no_upgrade}")
    print(f"  ✓ PASS" if no_upgrade == level_2_model else f"  ✗ FAIL")
    print()

    # Test 4: No upgrade for unknown model
    unknown_model = "some-unknown-model"
    print(f"Test 4: No upgrade for unknown model {unknown_model}")
    no_upgrade_unknown = upgrade_model_to_level_2(unknown_model, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)
    print(f"  Input: {unknown_model}")
    print(f"  Output: {no_upgrade_unknown}")
    print(f"  ✓ PASS" if no_upgrade_unknown == unknown_model else f"  ✗ FAIL")
    print()

    # Test 5: Test with realistic model values
    print("Test 5: Testing with realistic model values")

    # Test upgrading with the actual constants used in ProcessorAPI
    api_gemini_upgrade = upgrade_model_to_level_2("gemini-2.0-flash", "gemini-2.0-flash", "gpt-4o-mini")
    api_gpt_upgrade = upgrade_model_to_level_2("gpt-4o-mini", "gemini-2.0-flash", "gpt-4o-mini")

    print(f"  Gemini upgrade: gemini-2.0-flash -> {api_gemini_upgrade}")
    print(f"  Expected: {LLM_Handler.latest_medium_gemini_model()}")
    print(f"  ✓ PASS" if api_gemini_upgrade == LLM_Handler.latest_medium_gemini_model() else f"  ✗ FAIL")

    print(f"  GPT upgrade: gpt-4o-mini -> {api_gpt_upgrade}")
    print(f"  Expected: {LLM_Handler.latest_medium_openai_model()}")
    print(f"  ✓ PASS" if api_gpt_upgrade == LLM_Handler.latest_medium_openai_model() else f"  ✗ FAIL")
    print()

    print("=" * 50)
    print("Model upgrade testing completed!")

if __name__ == "__main__":
    test_model_upgrade()
