# Use Python 3.10 as base image
FROM python:3.10-slim

# Set environment variables for production
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    FLASK_DEBUG=0

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create non-root user first
RUN useradd --create-home --shell /bin/bash app

# Copy the rest of the application
COPY . .

# Convert line endings and make startup script executable
RUN dos2unix start.sh && \
    chmod +x start.sh && \
    mkdir -p temp temp_csvs && \
    chown -R app:app /app

# Switch to non-root user
USER app

# Expose the port the app runs on
EXPOSE 6060

# Use startup script that handles both production and development
CMD ["./start.sh"]

# Alternative production command using <PERSON><PERSON> directly (commented out)
# CMD ["gunicorn", "--config", "gunicorn.conf.py"]

# Alternative production command without config file (commented out)
# CMD ["gunicorn", "--bind", "0.0.0.0:6060", "--workers", "4", "--timeout", "300", "--keep-alive", "2", "--max-requests", "1000", "--max-requests-jitter", "100", "ProcessorAPI:app"]

# Development command (commented out)
# CMD ["python", "ProcessorAPI.py"]