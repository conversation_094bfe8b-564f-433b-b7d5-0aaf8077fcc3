import asyncio
import json
import uuid
import logging
from typing import Dict, Any, Callable, Optional, List, Tuple, Union, Awaitable

import aio_pika

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class MicroService:
    """Base class for a microservice that can send and receive commands over RabbitMQ."""
    
    def __init__(self, service_name: str, rabbitmq_url: str = "amqp://guest:guest@localhost/"):
        """
        Initialize a new microservice.
        
        Args:
            service_name: Unique name of this microservice
            rabbitmq_url: URL for connecting to RabbitMQ
        """
        self.service_name = service_name
        self.rabbitmq_url = rabbitmq_url
        self.logger = logging.getLogger(f"microservice.{service_name}")
        
        # Command handlers
        self._command_handlers: Dict[str, Callable] = {}
        
        # For tracking request-response pairs
        self._pending_responses: Dict[str, asyncio.Future] = {}
        
        # Connection objects
        self._connection = None
        self._channel = None
        self._exchange = None
        self._command_queue = None
        self._response_queue = None
        
        # For tracking consumer tags
        self._command_consumer = None
        self._response_consumer = None
        
        # For controlling the service lifecycle
        self._running = False
        self._stop_event = asyncio.Event()
        
        # Register the built-in stop command
        self.register_command("stop", self._handle_stop_command)
    
    def register_command(self, command_name: str, handler: Callable):
        """
        Register a command handler function.
        
        Args:
            command_name: Name of the command
            handler: Function to handle the command (will be wrapped to be async if it isn't already)
        """
        # Ensure the handler is an async function
        async def async_wrapper(*args, **kwargs):
            if asyncio.iscoroutinefunction(handler):
                return await handler(*args, **kwargs)
            else:
                # Run synchronous function in the executor pool
                return await asyncio.to_thread(handler, *args, **kwargs)
                
        self._command_handlers[command_name] = async_wrapper
        self.logger.info(f"Registered command: {command_name}")
    
    async def connect(self):
        """Establish connection to RabbitMQ and set up exchanges and queues."""
        self.logger.info(f"Connecting to RabbitMQ at {self.rabbitmq_url}")
        
        # Create connection
        self._connection = await aio_pika.connect_robust(self.rabbitmq_url)
        self._channel = await self._connection.channel()
        
        # Create topic exchange for commands
        self._exchange = await self._channel.declare_exchange(
            "microservices", aio_pika.ExchangeType.TOPIC
        )
        
        # Create queue for this service's commands
        self._command_queue = await self._channel.declare_queue(
            f"{self.service_name}.commands", durable=True
        )
        await self._command_queue.bind(self._exchange, routing_key=f"{self.service_name}.#")
        
        # Create queue for responses to this service
        self._response_queue = await self._channel.declare_queue(
            f"{self.service_name}.responses", durable=True
        )
        await self._response_queue.bind(self._exchange, routing_key=f"{self.service_name}.responses")
        
        self.logger.info("Connected to RabbitMQ")
    
    async def start(self):
        """Start the microservice by connecting and beginning to consume messages."""
        self._running = True
        self._stop_event.clear()
        
        await self.connect()
        
        # Start consuming command messages
        self._command_consumer = await self._command_queue.consume(self._process_command)
        self.logger.info(f"Started consuming commands for service: {self.service_name}")
        
        # Start consuming response messages
        self._response_consumer = await self._response_queue.consume(self._process_response)
        self.logger.info(f"Started consuming responses for service: {self.service_name}")
        
        # Wait for stop event
        await self._stop_event.wait()
        
        # Cleanup
        await self.stop()
    
    async def stop(self):
        """Stop the microservice and close connections."""
        if not self._running:
            return
            
        self._running = False
        self._stop_event.set()
        
        # Cancel consumers if they exist
        if self._command_consumer is not None:
            await self._command_queue.cancel(self._command_consumer)
            self.logger.debug("Cancelled command consumer")
        
        if self._response_consumer is not None:
            await self._response_queue.cancel(self._response_consumer)
            self.logger.debug("Cancelled response consumer")
        
        # Close connection
        if self._connection:
            await self._connection.close()
            self.logger.info("Closed RabbitMQ connection")
    
    async def _handle_stop_command(self, reason: str = "Remote stop command received"):
        """Handle the built-in stop command."""
        self.logger.info(f"Received stop command: {reason}")
        
        # Schedule the stop to happen after responding to the command
        asyncio.create_task(self._delayed_stop(), name="delated_stop")
        
        return {"status": "stopping", "reason": reason}
    
    async def _delayed_stop(self):
        """Stop the service after a short delay to allow response to be sent."""
        await asyncio.sleep(0.5)  # Brief delay to ensure response is sent
        await self.stop()
    
    async def _process_command(self, message: aio_pika.IncomingMessage):
        """Process an incoming command message."""
        async with message.process():
            try:
                # Parse the message
                payload = json.loads(message.body.decode())
                command = payload.get("command")
                args = payload.get("args", {})
                correlation_id = payload.get("correlation_id")
                reply_to = payload.get("reply_to")
                
                self.logger.info(f"Received command: {command} with args: {args}")
                
                # Check if we have a handler for this command
                if command in self._command_handlers:
                    handler = self._command_handlers[command]
                    
                    # Execute the command handler
                    try:
                        result = await handler(**args)
                        success = True
                        error = None
                    except Exception as e:
                        self.logger.exception(f"Error executing command {command}")
                        result = None
                        success = False
                        error = str(e)
                    
                    # If the sender expects a response, send one
                    if reply_to and correlation_id:
                        await self._send_response(reply_to, correlation_id, result, success, error)
                else:
                    self.logger.warning(f"Unknown command received: {command}")
                    
                    # If sender expects a response, inform them the command is unknown
                    if reply_to and correlation_id:
                        await self._send_response(
                            reply_to, 
                            correlation_id, 
                            None, 
                            False, 
                            f"Unknown command: {command}"
                        )
            except json.JSONDecodeError:
                self.logger.error("Failed to decode message as JSON")
            except Exception as e:
                self.logger.exception("Error processing command")
    
    async def _process_response(self, message: aio_pika.IncomingMessage):
        """Process an incoming response message."""
        async with message.process():
            try:
                # Parse the message
                payload = json.loads(message.body.decode())
                correlation_id = payload.get("correlation_id")
                
                self.logger.debug(f"Received response for correlation_id: {correlation_id}")
                
                # Check if we're waiting for this response
                if correlation_id in self._pending_responses:
                    future = self._pending_responses.pop(correlation_id)
                    if not future.done():
                        future.set_result(payload)
                else:
                    self.logger.warning(f"Received response with unknown correlation_id: {correlation_id}")
            except json.JSONDecodeError:
                self.logger.error("Failed to decode response as JSON")
            except Exception as e:
                self.logger.exception("Error processing response")
    
    async def _send_response(
        self, 
        destination: str, 
        correlation_id: str, 
        result: Any, 
        success: bool, 
        error: Optional[str] = None
    ):
        """Send a response back to the requesting service."""
        payload = {
            "correlation_id": correlation_id,
            "result": result,
            "success": success,
            "error": error
        }
        
        message = aio_pika.Message(
            body=json.dumps(payload).encode(),
            correlation_id=correlation_id
        )
        
        await self._exchange.publish(message, routing_key=destination)
        self.logger.debug(f"Sent response for correlation_id: {correlation_id}")
    
    async def call_service(
        self, 
        service_name: str, 
        command: str, 
        args: Dict[str, Any] = None, 
        wait_for_response: bool = True,
        timeout: float = 30.0
    ) -> Optional[Dict[str, Any]]:
        """
        Call a command on another service.
        
        Args:
            service_name: Name of the service to call
            command: Command to execute
            args: Arguments to pass to the command
            wait_for_response: Whether to wait for a response
            timeout: How long to wait for a response (in seconds)
            
        Returns:
            Response payload if wait_for_response is True, None otherwise
        """
        if args is None:
            args = {}
            
        correlation_id = str(uuid.uuid4())
        
        # Prepare the message payload
        payload = {
            "command": command,
            "args": args,
            "correlation_id": correlation_id,
        }
        
        # If we want a response, add necessary fields
        if wait_for_response:
            payload["reply_to"] = f"{self.service_name}.responses"
            
            # Create a future to receive the response
            response_future = asyncio.Future()
            self._pending_responses[correlation_id] = response_future
        
        # Create and send the message
        message = aio_pika.Message(
            body=json.dumps(payload).encode(),
            correlation_id=correlation_id
        )
        
        routing_key = f"{service_name}.commands"
        await self._exchange.publish(message, routing_key=routing_key)
        
        self.logger.info(f"Sent command {command} to service {service_name} with correlation_id: {correlation_id}")
        
        # If we're waiting for a response, wait for the future to complete
        if wait_for_response:
            try:
                response = await asyncio.wait_for(response_future, timeout=timeout)
                
                # Handle the response
                if response.get("success", False):
                    self.logger.info(f"Command {command} succeeded with result: {response.get('result')}")
                    return response.get("result")
                else:
                    error = response.get("error", "Unknown error")
                    self.logger.warning(f"Command {command} failed: {error}")
                    raise Exception(f"Remote command failed: {error}")
            except asyncio.TimeoutError:
                # Remove the pending future if we timed out
                self._pending_responses.pop(correlation_id, None)
                self.logger.error(f"Timeout waiting for response to command {command}")
                raise TimeoutError(f"Timeout waiting for response to command {command}")
        
        return None

    async def stop_service(self, service_name: str, reason: str = None, wait_for_response: bool = True):
        """
        Send a stop command to another service.
        
        Args:
            service_name: Name of the service to stop
            reason: Optional reason for stopping
            wait_for_response: Whether to wait for confirmation
            
        Returns:
            Response from the service if wait_for_response is True
        """
        args = {}
        if reason:
            args["reason"] = reason
            
        return await self.call_service(
            service_name, 
            "stop", 
            args, 
            wait_for_response=wait_for_response
        )

# Example usage
async def main():
    # Create a math service
    math_service = MicroService("math_service")
    
    # Define async math functions
    async def add(a, b):
        return a + b
        
    async def subtract(a, b):
        return a - b
        
    async def multiply(a, b):
        return a * b
        
    async def divide(a, b):
        if b == 0:
            raise ValueError("Cannot divide by zero")
        return a / b
    
    # Register commands with async functions
    math_service.register_command("add", add)
    math_service.register_command("subtract", subtract)
    math_service.register_command("multiply", multiply)
    math_service.register_command("divide", divide)
    
    # Start the service
    math_service_task = asyncio.create_task(math_service.start(), "math_service_task")
    
    # Create a client service
    client_service = MicroService("client_service")
    client_service_task = asyncio.create_task(client_service.start(), "client_service_task")
    
    # Give services time to start up
    await asyncio.sleep(1)
    
    # Call the math service
    try:
        # Synchronous call (wait for response)
        result = await client_service.call_service(
            "math_service", 
            "add", 
            {"a": 5, "b": 3}, 
            wait_for_response=True
        )
        print(f"Result of add: {result}")
        
        # Asynchronous call (fire and forget)
        await client_service.call_service(
            "math_service", 
            "multiply", 
            {"a": 10, "b": 2}, 
            wait_for_response=False
        )
        print("Sent multiply command without waiting for response")
        
        # Stop the math service remotely
        print("Stopping math service remotely...")
        await client_service.stop_service("math_service", reason="Testing remote stop")
        
        # Wait for the math service to stop
        await math_service_task
        print("Math service stopped")
        
        # Now stop the client service
        await client_service.stop()
        await client_service_task
        print("Client service stopped")
        
    except Exception as e:
        print(f"Error: {e}")
        # Ensure services are stopped even if an error occurs
        await math_service.stop()
        await client_service.stop()

if __name__ == "__main__":
    asyncio.run(main())