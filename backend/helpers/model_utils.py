"""
Model utility functions for automatic model upgrading and management.
"""

from helpers.LLM_Handler import LLM_Handler


def upgrade_model_to_level_2(model, default_gemini_model="gemini-2.0-flash", default_gpt_model="gpt-4o-mini"):
    """
    Automatically upgrade level 1 models to level 2 when level 2 is expected.
    
    Args:
        model (str): The model to potentially upgrade
        default_gemini_model (str): The default level 1 Gemini model to upgrade from
        default_gpt_model (str): The default level 1 GPT model to upgrade from
        
    Returns:
        str: The upgraded model or original model if no upgrade needed
    """
    if model == default_gemini_model or model == LLM_Handler.latest_small_gemini_model():
        return LLM_Handler.latest_medium_gemini_model()
    elif model == default_gpt_model or model == LLM_Handler.latest_small_openai_model():
        return LLM_Handler.latest_medium_openai_model()
    else:
        return model
