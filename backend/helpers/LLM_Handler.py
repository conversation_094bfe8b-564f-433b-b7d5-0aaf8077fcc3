import asyncio
import time


import os
import PIL.Image
import re
import yaml
from dotenv import load_dotenv
import base64
import json


#order: try to keep the best small model in front, the best medium model second last, and best large model last
openAIModels = ["gpt-4.1-mini", "gpt-4o-mini", "gpt-4o", "o3-mini", "o4-mini",  "gpt-4.1-nano", "gpt-4.1","o3"]
geminiModels = ["gemini-2.5-flash-lite","gemini-2.0-flash", "gemini-2.5-pro-exp-03-25", "gemini-2.5-flash", "gemini-2.5-pro"]
deprecatedModels = ["gemini-1.5-flash", "gemini-1.5-pro", "gemini-2.5-flash-preview-04-17","gemini-2.5-flash-preview-05-20","gemini-2.5-flash-lite-preview-06-17"]
claudeModels = ["claude-3-7-sonnet-20250219"]

OPENAI_MAX_INPUT_TOKENS = {
    "gpt-4o": 128_000,
    "gpt-4o-mini": 128_000, 
    "gpt-4.1": 1_047_576,
    "gpt-4.1-mini": 1_047_576,  
    "gpt-4.1-nano": 1_047_576, 
    "o3-mini": 200_000,
    "o4-mini": 200_000,
    "o3": 200_000
}

class LLM_Handler:
    def __init__(self, model="gpt-4.1-mini", is_free_version_of_gemini=False):
        """
        Initialize the LLM_Handler with API keys and model configurations.

        Args:
            model (str): The default model to use. Defaults to "gpt-4.1-mini".
            is_free_version_of_gemini (bool): Indicates if the free version of Gemini is used. Defaults to True.
        """
        
        load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))
        openai_api_key = os.getenv("OPENAI_API_KEY")
        gemini_api_key = os.getenv("GEMINI_API_KEY")
        gemini_backup_free_api_key = os.getenv("GEMINI_BACKUP_FREE_API_KEY")
        anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")

        if openai_api_key:
            from openai import OpenAI
            self.OpenAIClient = OpenAI(api_key=openai_api_key)
        else:
            print("Warning: No OpenAI API key provided. OpenAI functionality will not work.")

        if gemini_api_key or gemini_backup_free_api_key:
            from google import genai
            if gemini_api_key:
                self.GeminiClient = genai.Client(api_key=gemini_api_key)
            else:
                print("Warning: Using free gemini key as no paid key provided.")
                self.GeminiClient = genai.Client(api_key=gemini_backup_free_api_key)
                self.is_free_version_of_gemini = True
        else:
            print("Warning: No Gemini API key provided. Gemini functionality will not work.")
        if anthropic_api_key:
            import anthropic
            self.claude_client = anthropic.Anthropic(api_key=anthropic_api_key)
        else:
            print("Warning: No Anthropic API key provided. Claude functionality will not work.")
        
        if not (openai_api_key or gemini_api_key or gemini_backup_free_api_key or anthropic_api_key):
            raise Exception("No API keys provided. Please provide at least one API key.")
        
        self.model = self.check_has_appropriate_key(model, openai_api_key, gemini_api_key, gemini_backup_free_api_key, anthropic_api_key)
        self.is_free_version_of_gemini = is_free_version_of_gemini

    def check_has_appropriate_key(self, model, openai_api_key, gemini_api_key, gemini_backup_free_api_key, anthropic_api_key):
        provider = LLM_Handler.get_model_provider(model)

        # Check if the relevant API key for the selected provider is missing
        provider_key_missing = False
        if provider == "openai" and not openai_api_key:
            provider_key_missing = True
        elif provider == "gemini" and not (gemini_api_key or gemini_backup_free_api_key):
            provider_key_missing = True
        elif provider == "claude" and not anthropic_api_key:
            provider_key_missing = True

        if provider_key_missing:
            # Try to select a different model based on available API keys
            if openai_api_key:
                model = LLM_Handler.latest_small_openai_model()
                provider = "openai"
                print(f"Warning: No API key for selected provider. Switching to OpenAI model: {model}")
            elif gemini_api_key or gemini_backup_free_api_key:
                model = LLM_Handler.latest_small_gemini_model()
                provider = "gemini"
                print(f"Warning: No API key for selected provider. Switching to Gemini model: {model}")
            elif anthropic_api_key:
                model = claudeModels[0]
                provider = "claude"
                print(f"Warning: No API key for selected provider. Switching to Claude model: {model}")
            else:
                raise Exception("No API keys provided. Please provide at least one API key.")
        return model

    #---- STATIC METHODS ----
    @staticmethod
    def latest_small_gemini_model():
        return geminiModels[0] # "gemini-2.5-flash-lite-preview-06-17"
    @staticmethod
    def latest_medium_gemini_model():
        return geminiModels[-2] # "gemini-2.5-flash"
    @staticmethod
    def latest_large_gemini_model():
        return geminiModels[-1] # "gemini-2.5-pro

    @staticmethod
    def latest_small_openai_model():
        return openAIModels[0] # "gpt-4.1-mini"
    @staticmethod
    def latest_medium_openai_model():
        return openAIModels[-2] # "gpt-4.1"
    @staticmethod
    def latest_large_openai_model():
        return openAIModels[-1] # "o3"

    @staticmethod
    def convert_gpt_tools_to_claude_tools(gptToolsDeclaration):
        """
        Convert GPT tool declarations to Claude-compatible tool declarations.

        Args:
            gptToolsDeclaration (list): List of GPT tool declarations.

        Returns:
            list: List of Claude-compatible tool declarations.
        """
        return [
                {
                    "name": gptTool["function"]["name"],
                    "description": gptTool["function"]["description"],
                    "input_schema": gptTool["function"]["parameters"]
                } for gptTool in gptToolsDeclaration
        ]
    @staticmethod
    def convert_claude_tools_to_gpt_tools(claudeToolsDeclaration):
        """
        Convert Claude tool declarations to GPT-compatible tool declarations.

        Args:
            claudeToolsDeclaration (list): List of Claude tool declarations.

        Returns:
            list: List of GPT-compatible tool declarations.
        """
        return [
            {
                "type": "function",
                "function": {
                    "name": claudeTool["name"],
                    "description": claudeTool["description"],
                    "parameters": claudeTool["input_schema"]
                }
            } for claudeTool in claudeToolsDeclaration
        ]
    
    #--------------------Token Handling--------------------
    
    def get_token_count(self, text, model=None):
        
        if model==None: model=self.model

        if model in geminiModels:
            response = self.GeminiClient.models.count_tokens(
                model="gemini-2.0-flash", contents=text
            )
            total_tokens = response.total_tokens
            if total_tokens: return total_tokens
            print(f"Unable to get token count for gemini model {model}. Using 2.0 flash token count")
            return 1000000
        if (model in openAIModels):
            import tiktoken
            try:
                encoding = tiktoken.encoding_for_model(model)
            except KeyError:
                # A fallback or a way to handle models not in tiktoken
                print(f"No tiktoken encoding for model {model}, using generic gpt-4o for token count.")
                encoding = tiktoken.get_encoding("gpt-4o")
            return len(encoding.encode(text))
        else:
            #TODO: handle claude or any other model
            print(f"{model} is not known, using generic gpt-4o for token count.")
            encoding = tiktoken.get_encoding("gpt-4o")
            return len(encoding.encode(text))
    
    def get_max_input_tokens(self, model=None):
        if model==None: model=self.model
        if model in geminiModels:
            model_info = self.GeminiClient.models.get(model=model)
            return model_info.input_token_limit
        if (model in openAIModels):
           return OPENAI_MAX_INPUT_TOKENS[model]
        else:
            #TODO: handle claude or any other model
            print(f"{model} is not known, returning no token count")
            return None


    #--------------------GPT--------------------
    async def SendMessagesGPT_async(self,messages, model=None, tools = None,  tool_function = None):
        """
        Send messages to the GPT model.

        Args:
            messages (list): List of messages to send.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.

        Returns:
            str or tuple: The response from the GPT model.
        """
        model = self.__validate_openai_model(model)
        while True:
            completion = self.OpenAIClient.chat.completions.create(
                model=model,
                messages=messages,
                tools=tools if tools else None
            )

            msg = completion.choices[0].message
            if tools==None or msg.tool_calls==None: # or len(msg.tool_calls)==0:
                return msg.content
            #if tool use
            
            # Handle tool calls
            messages.append({
                "role": "assistant",
                "content": str(msg.content),
                "tool_calls": msg.tool_calls
            })
            for tool_call in msg.tool_calls:
                name = tool_call.function.name
                args = json.loads(tool_call.function.arguments)

                if asyncio.iscoroutinefunction(tool_function):
                    result = await tool_function(name, args)
                else:
                    result = tool_function(name, args)
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": str(result)
                })

    async def SendMessageGPT_async(self,question, system_prompt, model=None, tools = None, tool_function = None, image_path=None):
        """
        Send a single message to the GPT model.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.

        Returns:
            str or tuple: The response from the GPT model.
        """
        
        messages = self.__format_message_gpt(question, system_prompt, image_path)
        return await self.SendMessagesGPT_async(messages,model,tools, tool_function)

    def SendMessagesGPT(self,messages, model=None, tools = None,  tool_function = None):
        """
        Send messages to the GPT model.

        Args:
            messages (list): List of messages to send.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.

        Returns:
            str or tuple: The response from the GPT model.
        """
        model = self.__validate_openai_model(model)
        while True:
            retries = 0
            max_retries = 5
            wait_time = 30
            errors = {}
            while retries < max_retries:
                try:
                    completion = self.OpenAIClient.chat.completions.create(
                        model=model,
                        messages=messages,
                        tools=tools if tools else None
                    )
                    break
                except Exception as e:
                    retries += 1
                    if hasattr(e, 'code'):
                        code = e.code
                        message = str(e)
                        if code not in errors:
                            errors[code] = set()
                        errors[code].add(message)

                        if code == "insufficient_quota":
                            print("Insufficient quota - out of credits")
                            raise e
                        if code == 429 or "Error code: 429" in message:  # Rate limit error
                            print(f"OpenAI API rate limit hit. Retrying in {wait_time} seconds (attempt {retries}/{max_retries})...")
                            time.sleep(wait_time)
                        else:
                            print(f"An error occurred: {e}. Retrying (attempt {retries}/{max_retries})...")
                            time.sleep(1)  # Small delay for other exceptions
                    else:
                        print(f"An error occurred: {e}. Retrying (attempt {retries}/{max_retries})...")
                        time.sleep(1)
            
            if retries >= max_retries:
                raise Exception(f"Failed to get a response from OpenAI API after {max_retries} retries. Errors: {errors}")

            msg = completion.choices[0].message
            if tools==None or msg.tool_calls==None or len(msg.tool_calls)==0:
                return msg.content
            #if tool use
            
            if not tool_function:
                print("Attempting tool use but no tool function! Must handle externally")
                return msg, messages
            # Handle tool calls
            messages.append(msg)
            # messages.append({
            #     "role": "assistant",
            #     "content": str(msg.content),
            #     "tool_calls": msg.tool_calls
            # })

            for tool_call in msg.tool_calls:
                name = tool_call.function.name
                args = json.loads(tool_call.function.arguments)

                result = tool_function(name, args)
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": str(result)
                })

    def SendMessageGPT(self,question, system_prompt, model=None, tools = None, tool_function = None, image_path=None):
        """
        Send a single message to the GPT model.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.

        Returns:
            str or tuple: The response from the GPT model.
        """
        messages = self.__format_message_gpt(question, system_prompt, image_path)
        return self.SendMessagesGPT(messages,model,tools, tool_function)

    def __validate_openai_model(self, model):
        """
        Validate the provided OpenAI model and fallback to a default if invalid.

        Args:
            model (str): The model to validate.

        Returns:
            str: A valid OpenAI model.
        """
        if model==None: model=self.model

        if not (model in openAIModels):
            print(f"Error - trying to send openai message but using invalid model: {model}")
            print("Using fallback model:", openAIModels[0])
            model = openAIModels[0]
        return model

    def __format_message_gpt(self, question, system_prompt, image_path):
        """
        Format a message for GPT with optional image encoding.

        Args:
            question (str): The user question.
            system_prompt (str): The system prompt.
            image_path (str, optional): Path to an image to include. Defaults to None.

        Returns:
            list: Formatted message for GPT.
        """
        messages = [{"role": "system", "content": system_prompt}]

        if image_path is None:
            messages.append({"role": "user", "content": question})
        else:
            base64_image = self.encode_image(image_path)
            messages.append({
                "role": "user",
                "content": [
                    {"type": "text", "text": question},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
                ]
            })
            
        return messages

    #--------------------Gemini--------------------
    async def SendMessageGemini_async(self,question, system_prompt, model=None, tools = None, tool_function = None, image_path = None):
        """
        Send a single message to the Gemini model.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.

        Returns:
            str: The response from the Gemini model.
        """
        return await self.SendMessagesGemini_async([question],system_prompt, model=model, tools=tools,tool_function=tool_function, image_path=image_path)
    
    
    async def SendMessagesGemini_async(self,messages, system_prompt, model=None, tools = None, tool_function = None, max_retries = 5, wait_time = 30, image_path = None):
        """
        Send messages to the Gemini model with retry logic.

        Args:
            messages (list): List of messages to send.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.
            max_retries (int, optional): Maximum number of retries. Defaults to 5.
            wait_time (int, optional): Wait time between retries in seconds. Defaults to 30.

        Returns:
            str: The response from the Gemini model.
        """
        model = self.__validate_gemini_model(model)

        if image_path and os.path.exists(image_path):
            image = PIL.Image.open(image_path)
            messages.append(image)
        elif image_path:
            raise FileNotFoundError(f"The specified image path does not exist: {image_path}")
        
        if tool_function:
            return await self.__send_messages_gemini_manual_tool_calling_async(messages,system_prompt,model=model, tools=tools, tool_function=tool_function,max_retries=max_retries, wait_time=wait_time)
        else: return self.__send_contents_to_gemini(messages,system_prompt,model=model, tools=tools,max_retries=max_retries, wait_time=wait_time).text


    def SendMessageGemini(self,question, system_prompt, model=None, tools = None, tool_function = None, image_path = None, force_manual=False):
        """
        Send a single message to the Gemini model.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.
            force_manual (bool, optional): Whether to force manual tool calling. Defaults to False.

        Returns:
            str: The response from the Gemini model.
        """
        return self.SendMessagesGemini([question],system_prompt, model=model, tools=tools,tool_function=tool_function, image_path=image_path, force_manual=force_manual)
    
    
    def SendMessagesGemini(self,messages, system_prompt, model=None, tools = None, tool_function = None, max_retries = 5, wait_time = 30, image_path = None, force_manual=False):
        """
        Send messages to the Gemini model with retry logic.

        Args:
            messages (list): List of messages to send.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (optional): Tools to use. Defaults to None.
            max_retries (int, optional): Maximum number of retries. Defaults to 5.
            wait_time (int, optional): Wait time between retries in seconds. Defaults to 30.
            force_manual (bool, optional): Whether to force manual tool calling. Defaults to False.

        Returns:
            str: The response from the Gemini model.
        """
        model = self.__validate_gemini_model(model)

        if image_path and os.path.exists(image_path):
            image = PIL.Image.open(image_path)
            messages.append(image)
        elif image_path:
            raise FileNotFoundError(f"The specified image path does not exist: {image_path}")
        
        if tool_function or force_manual:
            return self.__send_messages_gemini_manual_tool_calling(messages,system_prompt,model=model, tools=tools, tool_function=tool_function,max_retries=max_retries, wait_time=wait_time)
        else: return self.__send_contents_to_gemini(messages,system_prompt,model=model, tools=tools,max_retries=max_retries, wait_time=wait_time).text
    
    def __send_messages_gemini_manual_tool_calling(self,messages, system_prompt, model=None, tools = None, tool_function = None, max_retries = 5, wait_time = 30):
        """
        Send messages to Gemini with manual tool calling.

        Args:
            messages (list): List of messages to send.
            system_prompt (str): The system prompt.
            model (str, optional): The model to use. Defaults to None.
            tools (list, optional): Tools to use. Defaults to None.
            tool_function (callable, optional): Function to handle tool calls. Defaults to None.
            max_retries (int, optional): Maximum retries. Defaults to 5.
            wait_time (int, optional): Wait time between retries in seconds. Defaults to 30.

        Returns:
            str: The response from Gemini.
        """
        from google.genai import types  
        contents = self.__format_gemini_messages_as_types(messages)
        
        while True:
            response = self.__send_contents_to_gemini(contents,system_prompt,model=model, tools=tools,max_retries=max_retries, wait_time=wait_time, manual_tool_calling=True)
            
            if not response or not response.candidates or not response.candidates[0].content:
                print("Received empty or invalid response from Gemini:", response)
                if response.candidates and len(response.candidates) > 0:
                    print("has candidate without content")
                    if hasattr(response.candidates[0], 'finish_reason') and response.candidates[0].finish_reason:
                        print("Finish reason:", response.candidates[0].finish_reason)
                
                return "ERROR: Received empty or invalid response from Gemini"
            
            candidate = response.candidates[0]
            #if tool use
            # It's possible for the model to stop without returning any parts, especially after a series of failed tool calls.

            if not hasattr(candidate.content, 'parts') or not candidate.content.parts:
                if hasattr(candidate, 'finish_reason') and candidate.finish_reason == 'STOP':
                    return "The model concluded its process without a final answer, likely due to repeated tool errors."
                else:
                    finish_reason = getattr(candidate, 'finish_reason', 'UNKNOWN')
                    print(f"Unexpected empty response from Gemini. Finish Reason: {finish_reason}")
                    return f"ERROR: Received an empty response with finish reason: {finish_reason}"
            
            # Check if any parts contain function calls
            has_function_calls = False
            for part in candidate.content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    has_function_calls = True
                    break
            
            if not has_function_calls:
                return response.text

            if not tool_function:
                print("Attempting tool use but no tool function! Must handle externally")
                return response, contents
            
            # Handle all function calls in all parts
            for part in candidate.content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    function_call = part.function_call
                    result = tool_function(function_call.name, function_call.args)
                    
                    function_response_part = types.Part.from_function_response(
                        name=function_call.name,
                        response={"result": result},
                    )
                    
                    contents.append(types.Content(role="model", parts=[types.Part(function_call=function_call)]))
                    contents.append(types.Content(role="user", parts=[function_response_part]))
           

    async def __send_messages_gemini_manual_tool_calling_async(self,messages, system_prompt, model=None, tools = None, tool_function = None, max_retries = 5, wait_time = 30):
        contents = self.__format_gemini_messages_as_types(messages)
        from google.genai import types  
        
        while True:
            response = self.__send_contents_to_gemini(contents,system_prompt,model=model, tools=tools,max_retries=max_retries, wait_time=wait_time, manual_tool_calling=True)

            msg = response.text
            #if tool use
            if not response.candidates[0].content.parts[0].function_call:
                return msg
            function_call = response.candidates[0].content.parts[0].function_call
            
            if asyncio.iscoroutinefunction(tool_function):
                result = await tool_function(function_call.name, function_call.args)
            else:
                result = tool_function(function_call.name, function_call.args)

            function_response_part = types.Part.from_function_response(
                name=function_call.name,
                response={"result": result},
            )
            contents.append(types.Content(role="model", parts=[types.Part(function_call=function_call)])) # Append the model's function call message
            contents.append(types.Content(role="user", parts=[function_response_part])) 

    def __format_gemini_messages_as_types(self, messages):
        """
        Format messages for Gemini as types.Content objects.

        Args:
            messages (list): List of messages to format.

        Returns:
            list: Formatted messages as types.Content objects.
        """
        from google.genai import types  
        contents = []
        for message in messages:
            if isinstance(message, str):
                contents.append(types.Content(
                    role="user", parts=[types.Part(text=message)]
                ))
            elif isinstance(message, PIL.Image.ImageFile):
                contents.append(types.Part.from_bytes(
                    data=message,
                    mime_type=message.get_format_mimetype()
                ))
            else:
                print("Gemini message of unexpected format:", message)
        return contents# Append the function response

    def __send_contents_to_gemini(self,messages, system_prompt, model=None, tools = None, max_retries = 5, wait_time = 30, manual_tool_calling=False):
        retries = 0
        errors = {}
        original_client = self.GeminiClient
        using_backup_key = False

        if self.GeminiClient is None:
            raise Exception("Gemini client is not initialized. Please provide a valid Gemini API key.")
        from google import genai 
        
        while retries < max_retries:
            try:         
                myConfig = genai.types.GenerateContentConfig(system_instruction=system_prompt, tools=tools, automatic_function_calling= {"disable": manual_tool_calling}) if tools else genai.types.GenerateContentConfig(system_instruction=system_prompt)
                response = self.GeminiClient.models.generate_content(
                    model=model,
                    config=myConfig,
                    contents=messages
                )
                # If we were using backup key, restore original client
                if using_backup_key:
                    self.GeminiClient = original_client
                return response
            except genai.errors.ClientError as e:
                retries += 1
                if hasattr(e, 'code') and hasattr(e, 'message'):
                    code = e.code
                    message = str(e.message)
                    if code not in errors:
                        errors[code] = set()
                    errors[code].add(message)

                if e.code == 429 and self.is_free_version_of_gemini: # Check status code and free version flag
                    print(f"Gemini API rate limit hit (free version). Retrying in {wait_time} seconds (attempt {retries}/{max_retries})...")
                    time.sleep(wait_time)
                elif e.code == 429: # Not free version, retry immediately
                    if hasattr(e, 'message'):
                        if "You exceeded your current quota" in e.message: 
                            print("QUOTA ERROR!")
                            # Try using backup free key if available
                            gemini_backup_free_api_key = os.getenv("GEMINI_BACKUP_FREE_API_KEY")
                            if not self.is_free_version_of_gemini and gemini_backup_free_api_key:
                                print("Switching to free backup Gemini API key...")
                                self.GeminiClient = genai.Client(api_key=gemini_backup_free_api_key)
                                using_backup_key = True
                                continue
                            else:
                                raise e
                    print(f"Gemini API rate limit hit. Retrying immediately (attempt {retries}/{max_retries})...")
                elif e.code == 503: #{'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}
                    print(f"An error occurred: {e}. Retrying in {wait_time} seconds (attempt {retries}/{max_retries})...")
                    time.sleep(wait_time)
                else: # Some other exception, re-raise it
                    print(f"An error occurred: {e}. Retrying (attempt {retries}/{max_retries})...")
                    time.sleep(1) # Small delay for other exceptions

            except Exception as e: # Catch other exceptions and handle retries
                retries += 1
                if hasattr(e, 'code') and e.code == 503: #{'code': 503, 'message': 'The model is overloaded. Please try again later.', 'status': 'UNAVAILABLE'}
                    print(f"An error occurred: {e}. Retrying in {wait_time} seconds (attempt {retries}/{max_retries})...")
                    time.sleep(wait_time)
                else:
                    print(f"An error occurred: {e}. Retrying (attempt {retries}/{max_retries})...")
                    time.sleep(1) # Small delay for other exceptions

        # If we were using backup key, restore original client before raising exception
        if using_backup_key:
            self.GeminiClient = original_client
            
        # If the loop completes without success, all retries failed
        raise Exception(f"Failed to get a response from Gemini API after {max_retries} retries. Errors: {errors}")

    def __validate_gemini_model(self, model):
        """
        Validate the provided Gemini model and fallback to a default if invalid.

        Args:
            model (str): The model to validate.

        Returns:
            str: A valid Gemini model.
        """
        if model==None: model=self.model
        if not (model in geminiModels):
            print(f"Error - trying to send gemini message but using invalid model: {model}")
            print("Using fallback model:", geminiModels[0])
            model = geminiModels[0]
        return model



    #--------------------Claude--------------------
    async def SendMessagesClaude_async(self, messages, system_prompt, model=None, tools=None, tool_function = None):
        """Sends messages to Antropic's chat API and handles tool calls iteratively."""
        model = self.__validate_claude_model(model)
        while True:
            if tools is None:
                response = self.claude_client.messages.create(
                    model=model,
                    max_tokens=1000,
                    system=system_prompt,
                    messages=messages,
                )
            else:
                response = self.claude_client.beta.messages.create(
                    model=model,
                    max_tokens=1000,
                    system=system_prompt,
                    messages=messages,
                    tools=tools,
                    betas=["token-efficient-tools-2025-02-19"]
                )
            # If no tool calls, return the final message content
            if len(response.content) == 1:
                return response.content[0].text

            # Handle tool calls
            messages.append({
                "role": "assistant",
                "content": response.content,
            })
            if len(response.content) > 1:
                tool_calls = response.content[1:]
                for tool_call in tool_calls:
                    if tool_call.type != "tool_use":
                        continue
                    tool_name = tool_call.name
                    tool_use_id = tool_call.id
                    tool_args = tool_call.input 
                    if asyncio.iscoroutinefunction(tool_function):
                        result = await tool_function(tool_name, tool_args)
                    else:
                        result = tool_function(tool_name, tool_args)
                    messages.append({
                        "role": "user",
                        "content": [{
                            "type": "tool_result",
                            "tool_use_id": tool_use_id,
                            "content": str(result)
                        }]
                    })
      
    async def SendMessageClaude_async(self, question, system_prompt, model=None, tools=None, tool_function = None):
        """Handles user input, optionally processes an image, and manages tool calls."""
        messages = [self.__format_claude_user_message(question)]
        return await self.SendMessagesClaude_async(messages, system_prompt, model,tools,tool_function)

    def SendMessagesClaude(self, messages, system_prompt, model=None, tools=None, tool_function = None):
        """Sends messages to Antropic's chat API and handles tool calls iteratively."""

        model = self.__validate_claude_model(model)
            
        
        while True:
            if tools is None:
                response = self.claude_client.messages.create(
                    model=model,
                    max_tokens=1000,
                    system=system_prompt,
                    messages=messages,
                )
            else:
                response = self.claude_client.beta.messages.create(
                    model=model,
                    max_tokens=1000,
                    system=system_prompt,
                    messages=messages,
                    tools=tools,
                    betas=["token-efficient-tools-2025-02-19"]
                )
            # If no tool calls, return the final message content
            if len(response.content) == 1:
                return response.content[0].text

            # Handle tool calls
            messages.append({
                "role": "assistant",
                "content": response.content,
            })
            if len(response.content) > 1:
                tool_calls = response.content[1:]
                for tool_call in tool_calls:
                    if tool_call.type != "tool_use":
                        continue
                    tool_name = tool_call.name
                    tool_use_id = tool_call.id
                    tool_args = tool_call.input 
                    result = tool_function(tool_name, tool_args)
                    messages.append({
                        "role": "user",
                        "content": [{
                            "type": "tool_result",
                            "tool_use_id": tool_use_id,
                            "content": str(result)
                        }]
                    })
      
    def SendMessageClaude(self, question, system_prompt, model=None, tools=None, tool_function = None):
        """Handles user input, optionally processes an image, and manages tool calls."""
        messages = [self.__format_claude_user_message(question)]
        return self.SendMessagesClaude(messages, system_prompt, model,tools,tool_function)

    def __format_claude_user_message(self, question):
        return {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": question
                        }
                    ]
                }

    def __validate_claude_model(self, model):
        """
        Validate the provided Claude model and fallback to a default if invalid.

        Args:
            model (str): The model to validate.

        Returns:
            str: A valid Claude model.
        """
        if not self.claude_client:
            print("Error - trying to send claude message but no claude key")
            raise Exception

        if model==None: model=self.model

        if not (model in claudeModels):
            print(f"Error - trying to send claude message but using invalid model: {model}")
            print("Using fallback model:", claudeModels[0])
            model = claudeModels[0]
        return model


    #--------------------Any LLM--------------------

    def sendMessageToLLM(self, question, system_prompt, GptTools=None, GeminiTools =None,ClaudeTools=None, tool_function = None,  model=None, image_path= None, force_manual=False):
        """
        Send a message to the appropriate LLM based on the model.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            GptTools (optional): Tools for GPT. Defaults to None.
            GeminiTools (optional): Tools for Gemini. Defaults to None.
            ClaudeTools (optional): Tools for Claude. Defaults to None.
            tool_function (optional): Function to handle tool calls. Defaults to None.
            model (str, optional): The model to use. Defaults to None.
            image_path (str, optional): Path to an image. Defaults to None.
            force_manual (bool, optional): Whether to force manual tool calling - only relevant to Gemini in current implementation. Defaults to False.

        Returns:
            str or tuple: The response from the LLM.
        """
        model = self.__validate_model(model)
        if model in openAIModels:
            if GptTools is None and ClaudeTools is not None:
                GptTools = self.convert_claude_tools_to_gpt_tools(ClaudeTools)
            return self.SendMessageGPT(question, system_prompt, model=model, tools=GptTools, tool_function=tool_function, image_path=image_path)
        if model in geminiModels:
            return self.SendMessageGemini(question, system_prompt, model=model, tools=GeminiTools, image_path=image_path, tool_function=tool_function, force_manual=force_manual) 
        if model in claudeModels:
            if ClaudeTools is None and GptTools is not None:
                ClaudeTools = self.convert_gpt_tools_to_claude_tools(GptTools)
            return self.SendMessageClaude(question,system_prompt,model=model, tools=ClaudeTools, tool_function=tool_function) # TODO: Claude image handling
        

        raise Exception(f"Error - trying to send gemini message but using invalid model: {self.model}")
    
    def sendMessagesToLLM(self, messages, system_prompt, GptTools=None, GeminiTools =None, ClaudeTools=None, tool_function = None, model=None, force_manual=False):
        """
        Send messages to the appropriate LLM based on the model.

        Args:
            messages (list): List of messages to send.
            system_prompt (str): The system prompt.
            GptTools (optional): Tools for GPT. Defaults to None.
            GeminiTools (optional): Tools for Gemini. Defaults to None.
            ClaudeTools (optional): Tools for Claude. Defaults to None.
            tool_function (optional): Function to handle tool calls. Defaults to None.
            model (str, optional): The model to use. Defaults to None.
            force_manual (bool, optional): Whether to force manual tool calling - only relevant to Gemini in current implementation. Defaults to False.

        Returns:
            str or tuple: The response from the LLM.
        """
        model = self.__validate_model(model)
        
        if model in openAIModels:
            formatted_messages = [{"role": "system", "content": system_prompt}]
            for message in messages:
                if isinstance(message, str):
                    formatted_messages.append({"role": "user", "content": message})
                    
                elif (isinstance(message, dict)): 
                    if message.get("role") != "system": #if system prompt is included already, don't include twice
                        formatted_messages.append(message)
                else:
                    #Example chat completion object that we may need to handle: ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_K9DFgIwBq0nFgsEKOGzWy3Ja', function=Function(arguments='{"dates": ["2024-10-01", "2025-04-30", "2025-10-01", "2026-04-30"]}', name='is_within_range'), type='function')], annotations=[])
                    formatted_messages.append(message)
            if GptTools is None and ClaudeTools is not None:
                GptTools = self.convert_claude_tools_to_gpt_tools(ClaudeTools)
            return self.SendMessagesGPT(formatted_messages, model=model, tools=GptTools, tool_function=tool_function)
        if model in geminiModels:
            return self.SendMessagesGemini(messages, system_prompt, model=model, tools=GeminiTools, tool_function=tool_function, force_manual=force_manual) 
        if model in claudeModels:
            if ClaudeTools is None and GptTools is not None:
                ClaudeTools = self.convert_gpt_tools_to_claude_tools(GptTools)
            return self.SendMessagesClaude(messages,system_prompt,model=model, tools=ClaudeTools, tool_function=tool_function)

        raise Exception(f"Error - trying to send message but using invalid model: {self.model}")

    def sendMessageToLLM_Expecting_Yaml_Response(self, question, system_prompt, GptTools=None, GeminiTools =None, ClaudeTools=None,tool_function=None, num_attempts = 3, field_to_extract = None):
        """
        Send a message to the appropriate LLM based on the model and expect a YAML response.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            GptTools (optional): Tools for GPT. Defaults to None.
            GeminiTools (optional): Tools for Gemini. Defaults to None.
            num_attempts (int, optional): Number of attempts to make. Defaults to 3.
            field_to_extract (str, optional): Field to extract from the response. If none, returns the entire response. Defaults to None.

        Returns:
            str or tuple: The response from the LLM.
        """
        attempt = 0
        while attempt < num_attempts:
            response = self.sendMessageToLLM(question,system_prompt, GptTools=GptTools, GeminiTools=GeminiTools, ClaudeTools=ClaudeTools,tool_function=tool_function)
            try:
                parsed_response = self.parse_yaml(response)
                if field_to_extract:
                    return parsed_response[field_to_extract]
                return parsed_response
            except:
                attempt += 1
            
        return {"error": "Failed to parse yaml response correcting element after multiple attempts."} 
    
    def sendMessageToLLM_Expecting_JSON_Response(self, question, system_prompt, GptTools=None, GeminiTools =None, ClaudeTools=None, tool_function=None, num_attempts = 3, field_to_extract = None):
        """
        Send a message to the appropriate LLM based on the model and expect a JSON response.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            GptTools (optional): Tools for GPT. Defaults to None.
            GeminiTools (optional): Tools for Gemini. Defaults to None.
            num_attempts (int, optional): Number of attempts to make. Defaults to 3.
            field_to_extract (str, optional): Field to extract from the response. If none, returns the entire response. Defaults to None.

        Returns:
            str or tuple: The response from the LLM.
        """
        attempt = 0
        while attempt < num_attempts:
            response = self.sendMessageToLLM(question,system_prompt, GptTools=GptTools, GeminiTools=GeminiTools, ClaudeTools=ClaudeTools, tool_function=tool_function)
            try:
                parsed_response = self.parse_json(response)
                if field_to_extract:
                    return parsed_response[field_to_extract]
                return parsed_response
            except:
                attempt += 1

        return {"error": "Failed to parse json response correcting element after multiple attempts."}

    async def sendMessageToLLM_async(self, question, system_prompt, GptTools=None, GeminiTools =None,ClaudeTools=None, tool_function = None,  model=None, image_path= None):
        """
        Send a message to the appropriate LLM based on the model.

        Args:
            question (str): The question to ask.
            system_prompt (str): The system prompt.
            GptTools (optional): Tools for GPT. Defaults to None.
            GeminiTools (optional): Tools for Gemini. Defaults to None.

        Returns:
            str or tuple: The response from the LLM.
        """
        model = self.__validate_model(model)
        if model in openAIModels:
            if GptTools is None and ClaudeTools is not None:
                GptTools = self.convert_claude_tools_to_gpt_tools(ClaudeTools)
            return await self.SendMessageGPT_async(question, system_prompt, model=model, tools=GptTools, tool_function=tool_function, image_path=image_path)
        if model in geminiModels:
            return await self.SendMessageGemini_async(question, system_prompt, model=model, tools=GeminiTools, image_path=image_path, tool_function=tool_function) 
        if model in claudeModels:
            if ClaudeTools is None and GptTools is not None:
                ClaudeTools = self.convert_gpt_tools_to_claude_tools(GptTools)
            return await self.SendMessageClaude_async(question,system_prompt,model=model, tools=ClaudeTools, tool_function=tool_function) # TODO: Claude image handling

    async def sendMessagesToLLM_async(self, messages, system_prompt, GptTools=None, GeminiTools =None, ClaudeTools=None, tool_function = None, model=None):
        """
        Send messages to the appropriate LLM based on the model.

        Args:
            messages (list): List of messages to send.
            system_prompt (str): The system prompt.
            GptTools (optional): Tools for GPT. Defaults to None.
            GeminiTools (optional): Tools for Gemini. Defaults to None.
            ClaudeTools (optional): Tools for Claude. Defaults to None.
            tool_function (optional): Function to handle tool calls. Defaults to None.
            model (str, optional): The model to use. Defaults to None.

        Returns:
            str or tuple: The response from the LLM.
        """
        model = self.__validate_model(model)
        
        if model in openAIModels:
            formatted_messages = [{"role": "system", "content": system_prompt}]
            for message in messages:
                formatted_messages.append({"role": "user", "content": message})
            if GptTools is None and ClaudeTools is not None:
                GptTools = self.convert_claude_tools_to_gpt_tools(ClaudeTools)
            return await self.SendMessagesGPT_async(formatted_messages, model=model, tools=GptTools, tool_function=tool_function)
        if model in geminiModels:
            return await self.SendMessagesGemini_async(messages, system_prompt, model=model, tools=GeminiTools, tool_function=tool_function) 
        if model in claudeModels:
            if ClaudeTools is None and GptTools is not None:
                ClaudeTools = self.convert_gpt_tools_to_claude_tools(GptTools)
            return await self.SendMessagesClaude__async(messages,system_prompt,model=model, tools=ClaudeTools, tool_function=tool_function)

        raise Exception(f"Error - trying to send message but using invalid model: {self.model}")


    def __validate_model(self, model):
        """
        Validate the provided model across all supported LLMs and fallback to a default if invalid.

        Args:
            model (str): The model to validate.

        Returns:
            str: A valid model.
        """
        model = model if model else self.model
        provider = LLM_Handler.get_model_provider(model)

        if provider == "unknown":
            print(model,"model not found - using default:", self.model)
            model = self.model
        elif provider == "deprecated":
            print(model,"model is deprecated - using default:", self.model)
            model = self.model
        return model
    
    @staticmethod
    def get_model_provider(model):
         
        if model in openAIModels:
            return "openai"
        if model in geminiModels:
            return "gemini"
        if model in claudeModels:
            return "claude"
        if model in deprecatedModels:
            return "deprecated"
        return "unknown"



    def encode_image(self, image_path):
        """
        Encode an image to a base64 string.

        Args:
            image_path (str): Path to the image file.

        Returns:
            str: Base64-encoded string of the image.
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")
    
    def clean(self, ai_response_to_clean):
        """
        Clean AI response by extracting relevant content based on code blocks.

        Args:
            ai_response_to_clean (str): The AI response to clean.

        Returns:
            str: Cleaned AI response.
        """
        #clean input
        if '```yaml' in ai_response_to_clean:
            ai_response_to_clean = ai_response_to_clean.split('```yaml')[1].split('```')[0]
        elif '```lookml' in ai_response_to_clean:
            ai_response_to_clean = ai_response_to_clean.split('```lookml')[1].split('```')[0]
        elif '```json' in ai_response_to_clean:
            ai_response_to_clean = ai_response_to_clean.split('```json')[1].split('```')[0]
        return ai_response_to_clean.strip()
     
    def parse_yaml(self, response):
        """
        Parse a YAML response and fix common formatting issues.

        Args:
            response (str): The YAML response to parse.

        Returns:
            dict: Parsed YAML response.
        """
        def fix_yaml(yaml_str):
            return fix_yaml_indentation(fix_yaml_quotes(yaml_str))

        def fix_yaml_indentation(yaml_str):
            """
            Ensures properties under lists and nested objects are properly indented.
            """
            lines = yaml_str.split("\n")
            fixed_lines = []
            indent_level = 0  # Track expected indentation
            inside_list = False  # Flag for list item tracking

            for i, line in enumerate(lines):
                stripped = line.lstrip()
                indent_size = len(line) - len(stripped)  # Get the current indentation level

                # Detect list items (- key:)
                if re.match(r"^\s*-\s+\w+:", line):
                    inside_list = True
                    indent_level = indent_size + 2  # Expect next properties to be indented under this list item
                    fixed_lines.append(line)
                    continue

                # If inside a list and we find a key-value pair without a dash, indent it
                if inside_list and re.match(r"^\w+: ", stripped):  
                    fixed_lines.append(" " * indent_level + stripped)
                    continue

                # Handle nested objects (like ui_config:)
                if re.match(r"^\w+:$", stripped):  # Detect keys without values (nested dicts)
                    fixed_lines.append(" " * indent_level + stripped)
                    indent_level += 2  # Increase indent for nested properties
                    continue

                # Add lines normally if no special formatting is needed
                fixed_lines.append(line)

            return "\n".join(fixed_lines)

        
        def fix_yaml_quotes(yaml_str):
            """
            Replaces incorrect YAML escape sequences (\') with proper double single quotes ('') 
            to maintain valid YAML formatting.
            """
            return yaml_str.replace("\\'", "''")  # Replace \'
        
        cleaned = fix_yaml(self.clean(response))
        try:
            my_yaml = yaml.safe_load(cleaned)
            return my_yaml
        except yaml.YAMLError as exc:
            print(f"YAMLError loading yaml:\n{cleaned}\n", exc)
            raise exc
        except Exception as exc:
            print(f"error loading yaml:\n{cleaned}\n", exc)
            raise exc
    
    def parse_json(self, json_str):
        """
        Parse a JSON string and handle errors.

        Args:
            json_str (str): The JSON string to parse.

        Returns:
            dict: Parsed JSON object.
        """
        json_str = self.clean(json_str)
        # Fix Python literals that are invalid in JSON
        json_str = json_str.replace('None', 'null')
        json_str = json_str.replace('True', 'true')
        json_str = json_str.replace('False', 'false')
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as exc:
            print(f"error loading json:\n{json_str}\n", exc)
            raise exc

if __name__=="__main__":
    handler = LLM_Handler()
    lorum_ipsum = """
What is Lorem Ipsum?

Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
Why do we use it?

It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters, as opposed to using 'Content here, content here', making it look like readable English. Many desktop publishing packages and web page editors now use Lorem Ipsum as their default model text, and a search for 'lorem ipsum' will uncover many web sites still in their infancy. Various versions have evolved over the years, sometimes by accident, sometimes on purpose (injected humour and the like).

Where does it come from?

Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of "de Finibus Bonorum et Malorum" (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, "Lorem ipsum dolor sit amet..", comes from a line in section 1.10.32.

The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from "de Finibus Bonorum et Malorum" by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.
Where can I get some?

There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc."""
    print("Max tokens:", handler.get_max_input_tokens())
    print("Tokens in lorum ipsum:", handler.get_token_count(lorum_ipsum))
    print("Characters in lorum_ipsum:", len(lorum_ipsum))
