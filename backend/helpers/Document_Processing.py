from pypdf import Pdf<PERSON><PERSON><PERSON>, PdfWriter
import os
import glob
import pandas as pd
from pdf2image import convert_from_path
#import pytesseract
from tabulate import tabulate
from openpyxl import load_workbook
import re
from .LLM_Handler import LLM_Handler
import regex
from dotenv import load_dotenv
#from mistralai import Mistral
#import base64

from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import AnalyzeDocumentRequest
#azure_endpoint = "https://sapconet-idp.cognitiveservices.azure.com/"
#azure_key = "1OQLFJUq4Av4ViA8WIHcSbAw39JHx9SxENscgQSSn1f4DfsRAgXVJQQJ99BEACYeBjFXJ3w3AAALACOGgofl"

load_dotenv()
azure_endpoint = os.getenv("DOC_INTELLIGENCE_AZURE_ENDPOINT")
azure_key = os.getenv("DOC_INTELLIGENCE_AZURE_KEY")



class Doc_Processor:

    def list_pdf_paths(self, directory):
        """
        List all PDF paths in a directory.

        Args:
            directory (str): The directory to search for PDFs.

        Returns:
            list: List of absolute paths to PDF files.
        """
        # Use glob to find all PDFs in the directory
        pdf_files = glob.glob(os.path.join(directory, '*.pdf'))
        # Convert relative paths to absolute paths
        pdf_files_absolute = [os.path.abspath(pdf) for pdf in pdf_files]
        return pdf_files_absolute

    def read_pdf_to_string_basic(self, pdf_path):
        """
        Read a PDF file and extract its text content.

        Args:
            pdf_path (str): The path to the PDF file.

        Returns:
            str: The extracted text content.
        """
        reader = PdfReader(pdf_path)
        text = ""
        for page in reader.pages:
            text += page.extract_text() + "\n"
        return text
    
    def read_pdf_to_string(self, pdf_path, force_ocr=False, provider = None):
        """
        Read a PDF file and extract its text content, using the best available method.

        Args:
            pdf_path (str): The path to the PDF file.
            force_ocr (bool, optional): Whether to force OCR. Defaults to False.
            provider (str, optional): The OCR provider to use, if using OCR. Defaults to None. Can specify from the following: azure, gemini

        Returns:
            str: The extracted text content.
        """
        pdfstr = self.read_pdf_to_string_basic(pdf_path) if not force_ocr else self.read_pdf_to_string_ocr_with_cache(pdf_path, provider=provider)
        if pdfstr is None or len(pdfstr.strip())==0:
            print("Error on pdf parsing - no document contents found. Trying with OCR...")
            pdfstr = self.read_pdf_to_string_ocr_with_cache(pdf_path, provider=provider)
            if pdfstr is None or len(pdfstr.strip())==0:
                print("Error on pdf parsing - no document contents found.")
                return
            print("OCR successful.")
        
        return pdfstr

       
    
    # def ocr_pdf_spacy(self, pdf_path):
    #     """
    #     Perform OCR on a PDF using spaCy.

    #     Args:
    #         pdf_path (str): The path to the PDF file.

    #     Returns:
    #         str: The extracted text content in Markdown format.
    #     """
    #     nlp = spacy.blank("en")
    #     layout = spaCyLayout(nlp)
    #     doc = layout(pdf_path)
    #     return doc._.markdown
    
    def ocr_pdf_gemini(self, pdf_path):
        """
        Perform OCR on a PDF using the Gemini model.

        Args:
            pdf_path (str): The path to the PDF file.

        Returns:
            str: The extracted text content in Markdown format.
        """
        images = convert_from_path(pdf_path)
        ocr_prompt = """OCR the following page into Markdown. Tables should be formatted as HTML."""
        temp_dir = "temp_pdf_images"
        os.makedirs(temp_dir, exist_ok=True)
        chunks = []
        for i, image in enumerate(images):
            image_path = os.path.join(temp_dir,  f"image_{i}.png")
            image.save(image_path)
            response = LLM_Handler("gemini-2.5-flash-preview-04-17").sendImageToGemini(ocr_prompt, [], image_path=image_path)
            if '```markdown' in response:
                response = response.split('```markdown')[1].split('```')[0]
            chunks.append(response)

        # Clean up temporary files
        for file in os.listdir(temp_dir):
            os.remove(os.path.join(temp_dir, file))
        os.rmdir(temp_dir)

        full_md = "\n".join(chunks)
        return full_md

    # def ocr_pdf_pytesseract(self, pdf_path):
    #     """
    #     Perform OCR on a PDF using Tesseract.

    #     Args:
    #         pdf_path (str): The path to the PDF file.

    #     Returns:
    #         str: The extracted text content.
    #     """
    #     images = convert_from_path(pdf_path)
    #     text = ""
    #     #Extract text from each image using Tesseract
    #     for image in images:
    #        text += pytesseract.image_to_string(image) + "\n"
    #     return text  
        
    
    def read_pdf_to_string_ocr_with_cache(self, pdf_path, provider = None):
        """
        Read a PDF file and extract its text content using OCR with caching.

        Args:
            pdf_path (str): The path to the PDF file.
            provider (str): The OCR provider to use. Defaults to None. Can specify from the following: azure, gemini

        Returns:
            str: The extracted text content.
        """
        # Convert PDF to images

        # Create cache directory if it doesn't exist
        cache_dir = "doc_cache"
        os.makedirs(cache_dir, exist_ok=True)
    
        # Generate cache filename using the original pdf name + _ocr + .txt or .md
        pdf_filename = os.path.basename(pdf_path)
        cache_filename = pdf_filename.rsplit('.', 1)[0] + '_ocr.txt'
        txt_path = os.path.join(cache_dir, cache_filename)
        if os.path.exists(txt_path):
            text_path = txt_path
        else:
            cache_filename = pdf_filename.rsplit('.', 1)[0] + '_ocr.md'
            text_path = os.path.join(cache_dir, cache_filename)
        
        # If cache exists, read from it
        if os.path.exists(text_path):
            print("Found cached OCR result, reading from file", text_path)
            with open(text_path, 'r') as f:
                return f.read()
        
        print(f"No cached OCR result found (searching for {text_path}), performing OCR...")

        provider = provider.lower() if provider else None
        if provider == "azure":
            try:
                ocr_text = self.ocr_pdf_azure(pdf_path)
            except Exception as e:
                print("Exception using azure for ocr (using gemini as fallback):", e)
                ocr_text= self.ocr_pdf_gemini(pdf_path)
        elif provider == "gemini":
            try:
                ocr_text = self.ocr_pdf_gemini(pdf_path)
            except Exception as e:
                print("Exception using gemini for ocr (using azure as fallback):", e)
                ocr_text= self.ocr_pdf_azure(pdf_path)
        else:

            #default OCR - try azure as it seems best. Then fallback with gemini
            try:
                ocr_text = self.ocr_pdf_azure(pdf_path)
            except Exception as e:
                #print("Exception using gemini for ocr (using pytesseract as fallback):", e)
                #ocr_text = self.ocr_pdf_pytesseract(pdf_path)
                print("Exception using azure for ocr (using gemini as fallback):", e)
                ocr_text= self.ocr_pdf_gemini(pdf_path)


        # Save the OCR result
        if ocr_text:  # Only save if we got a result
            print("Saving OCR result to cache...")
            with open(text_path, 'w') as f:
                f.write(ocr_text)
        return ocr_text

   
    def read_excel_to_string(self, file_path, sheet_name=None):
        """
        Read an Excel file and extract its content as a string.

        Args:
            file_path (str): The path to the Excel file.
            sheet_name (str, optional): The name of the sheet to read. Defaults to None.

        Returns:
            str: The extracted content as a formatted string.
        """
        # Load the workbook
        wb = load_workbook(file_path, data_only=True)
        ws = wb[sheet_name] if sheet_name!= None else wb[wb.sheetnames[0]]
        # Get merged cell ranges
        merged_ranges = ws.merged_cells.ranges

        # Create a dictionary to store merged cell values
        merged_cells_dict = {}
        for merged_range in merged_ranges:
            top_left_cell = merged_range.start_cell  # Top-left cell of the merged range
            value = top_left_cell.value  # Value of the merged cell
            for row in ws[merged_range.coord]:  # Iterate over all cells in the merged range
                for cell in row:
                    merged_cells_dict[(cell.row, cell.column)] = value  # Map each cell to the merged value

        # Load the data into a DataFrame
        #data = ws.values
        data = []
        for row in ws.iter_rows(values_only=True):  # Use `values_only=True` to get computed values
            data.append(row)


        df = pd.DataFrame(data)


        # Replace cells in merged ranges with their merged values
        for (row, col), value in merged_cells_dict.items():
            df.iloc[row - 1, col - 1] = value  # Adjust for 0-based indexing in pandas

        df = df.fillna('')  # Replace NaN with empty strings
        

        # Convert the DataFrame to a formatted string
       
        formatted_string = self.formatDataFrame("natural language",df)

        context = f"""
        Data extracted from sheet: {ws.title}
        Data extracted from file: {os.path.basename(file_path)}
        Number of rows: {len(df)}
        Number of columns: {len(df.columns)}

        """

        formatted_string = context + "\n" + formatted_string
                
        return formatted_string

    def __get_excel_column_label(self, idx):
        """Convert a 0-based index to Excel-style column label (e.g., 0 -> 'A', 27 -> 'AB')"""
        label = ""
        while idx >= 0:
            label = chr(idx % 26 + 65) + label
            idx = idx // 26 - 1
        return label

    def formatDataFrame(self, format, df):
        """
        Format a DataFrame into a specified format.

        Args:
            format (str): The format to use (e.g., "basic", "keyval", "table", "md", "json", "natural language").
            df (pd.DataFrame): The DataFrame to format.

        Returns:
            str: The formatted DataFrame as a string.
        """
        if format=="basic":
            pd.set_option('display.max_colwidth', None)  # Prevents truncation of long text
            return df.to_string(index=False)
        elif format=="keyval":
            # Format as key-value pairs
            data_dict = df.to_dict(orient='records')
            formatted_string = ""
            for row in data_dict:
                for key, value in row.items():
                    formatted_string += f"{key}: {value}\n"
                formatted_string += "\n"  # Add a separator between rows
            return formatted_string
        elif format=="table":
            return tabulate(df, headers='keys', tablefmt='pretty', showindex=False)
        elif format=="md":
            return tabulate(df, headers='keys', tablefmt='github') 
        elif format=="json":
            return df.to_json(orient='records', indent=2)
        elif format == "natural language":
            formatted_string = ""
            col_labels = [self.__get_excel_column_label(i) for i in range(len(df.columns))]

            for index, row in df.iterrows():
                row_items = [(col_labels[i], val if not pd.isna(val) else "") for i, val in enumerate(row)]

                # Trim trailing empty values
                while row_items and row_items[-1][1] == "":
                    row_items.pop()
                if not row_items:
                    continue

                formatted_string += f"Row {index + 1}:\n"
                i = 0
                while i < len(row_items):
                    start_label, val = row_items[i]
                    j = i + 1
                    # Group consecutive identical values
                    while j < len(row_items) and row_items[j][1] == val:
                        j += 1
                    if j - i > 1 and val != "":
                        formatted_string += f"  - Columns {start_label} - {row_items[j-1][0]}: {val}\n"
                    elif val != "":
                        formatted_string += f"  - Column {start_label}: {val}\n"
                    i = j
                formatted_string += "\n"
            return formatted_string.strip()


    def chunk_doc_if_needed(self, doc, LLM_handler: LLM_Handler, length_threshold=5000, length_is_tokens=False):
        """
        Chunk a document into sections if it exceeds a certain length threshold.

        Args:
            doc (str): The document to chunk.
            length_threshold (int, optional): The length threshold to chunk the document. Defaults to 5000.

        Returns:
            tuple: A tuple containing the list of headings and the list of sections.

        """
        if length_is_tokens:
            num_tokens = LLM_handler.get_token_count(doc)
        else:
            num_tokens = len(doc)

        if num_tokens < length_threshold:
            # Treat the whole document as a single section
            headings = ["Full Document"]
            sections = [doc]
        else:
            # Chunk the document into sections
            headings, sections = self.__chunk_doc(doc, LLM_handler)
        return headings, sections

    def __chunk_doc(self,doc, LLM_handler):
        """
        Chunk a document into sections based on headings.

        Args:
            doc (str): The document to chunk.

        Returns:
            tuple: A tuple containing the list of headings and the list of sections.
        """
        ChunkerPrompt = """What are the headings of the MAIN sections in the following document? Carefully read through the document to identify distinct sections.
        These headings will be used to split the document, so each heading should describe the immediate document section that follows, and be explicitly contained within the text.
        Consider primary/major sections only, avoiding subsections. You should anticipate between 3 and 10 distinct primary sections. If headings share the same name, this might be a sign that this should actually be considered a subsection, rather than a distinct section.
        List only each heading on a new line, for example:
        INDEX
        Our Story
        Accommodations
        Experiences"""

        headingsraw = LLM_handler.sendMessageToLLM(doc,ChunkerPrompt)
        print("headings:",headingsraw)
        headings = headingsraw.split("\n")

        ValidatorPrompt = f"""Are the following headings the best way to separate the major sections in the following document?\n{headingsraw}\n These headings will be used to split the document, so each heading should describe the immediate major document section that follows, and be explicitly contained within the text.
        If all of the headings are situated very early on in the document, for example, this may indicate that the full document has not been properly considered.
        Return a YAML response with the following data:
        REASONING: the rationale behind any changes in headings you decide to make.
        CORRECT_HEADINGS: a list of the correct headings for the major sections of the document."""

        #response = self.SendMessageGPT(doc,ValidatorPrompt)
        #parsed_response = self.parse_yaml(response)
        #headings = parsed_response["CORRECT_HEADINGS"]

        headings = [heading.strip() for heading in headings if heading.strip()]


        sections = self.split_text_by_headings(doc, headings)

        return headings, sections
    
    def __headings_are_sequential(self, text, headings, max_errors=2):
        """
        Check if the headings are sequential in the text, allowing for fuzzy matches.

        Args:
            text (str): The text to check.
            headings (list): The list of heading strings in the expected sequential order.
            max_errors (int): Maximum allowed errors (insertions, deletions, substitutions)
                              for a heading to be considered a match.

        Returns:
            bool: True if the headings are found sequentially, False otherwise.
        """
        current_search_offset = 0
        for heading_text in headings:
            # (?b) enables approximate "whole word" matching for the fuzzy pattern.
            # {e<=N} allows up to N errors (s:substitutions, i:insertions, d:deletions).
            # You can be more specific, e.g., {s<=1,i<=1,d<=0} for 1 sub and 1 ins.
            pattern = rf'(?b)({regex.escape(heading_text)}){{e<={max_errors}}}'
            try:
                # Search in the remainder of the text.
                # re.BESTMATCH tries to find the best possible fuzzy match.
                match = regex.search(pattern, text[current_search_offset:], flags=regex.BESTMATCH)
                
                if not match:
                    # For debugging:
                    print(f"Debug: Heading '{heading_text}' (index {i}) not found after offset {current_search_offset} with max_errors={max_errors}.")
                    return False  # Current heading not found in sequence
                
                # If found, the next search must start after this match ends.
                # match.end() is relative to the slice text[current_search_offset:]
                current_search_offset += match.end()
            except Exception as e:
                # For debugging:
                print(f"Debug: Error during sequential check for '{heading_text}': {e}")
                return False # Error during search, treat as not sequential
        return True
    
    def get_sequential_headings(self, text, headings, max_errors=2):
        """
        Finds the given headings in the text using fuzzy matching and returns them
        ordered by their appearance. Returns detailed match information.

        Args:
            text (str): The text to check.
            headings (list): The list of heading strings extracted by the LLM.
            max_errors (int): Maximum allowed errors for a heading match.

        Returns:
            list: A list of dictionaries, where each dictionary contains information
                  about a found heading (llm_heading, matched_span, start, end, errors).
                  The list is sorted by the 'start' index.
        """
        found_matches = []
        for heading_text in headings:
            # (?b) for approximate "whole word" matching.
            pattern = rf'(?b)({regex.escape(heading_text)}){{e<={max_errors}}}'
            try:
                # Search the entire text for the best match of this specific heading.
                match = regex.search(pattern, text, flags=regex.BESTMATCH)
                if match:
                    found_matches.append({
                        'llm_heading': heading_text,      # The heading string from the input list
                        'matched_span': match.group(0),   # The actual text span that matched fuzzily
                        'start': match.start(),           # Start index in the original text
                        'end': match.end(),               # End index in the original text
                        'errors': match.fuzzy_counts      # Tuple (substitutions, insertions, deletions)
                    })
                # else:
                    # For debugging:
                    # print(f"Debug: Heading '{heading_text}' not found in get_sequential_headings with max_errors={max_errors}.")
            except Exception as e:
                # For debugging:
                print(f"Debug: Error processing heading '{heading_text}' in get_sequential_headings: {e}")
                pass # Skip if a heading causes an error, or log it as needed

        # Sort the found matches by their start position in the text
        found_matches.sort(key=lambda x: x['start'])
        
        # To get just the list of LLM heading strings in order of appearance (like original):
        sequential_llm_strings = [match_data['llm_heading'] for match_data in found_matches]
        return sequential_llm_strings

        # Returning the full match data is generally more useful for text segmentation
        #return found_matches
    
    def __normalize_heading(self, heading):
        """
        Normalize a heading by removing punctuation and converting to uppercase.

        Args:
            heading (str): The heading to normalize.

        Returns:
            str: The normalized heading.
        """
        return re.sub(r"[^\w\s]", "", heading).upper()
    
    def __create_position_mapping(self, original_text):
        """
        Create a mapping between normalized text positions and original text positions.

        Args:
            original_text (str): The original text.

        Returns:
            tuple: A tuple containing the normalized text and a list mapping each normalized position to its original position.
        """
        normalized = []  # List to store normalized characters
        position_map = []  # List to store original positions for each normalized character
        
        i = 0  # Position in original text
        while i < len(original_text):
            # Get the next chunk (could be a single character or more)
            chunk = original_text[i]
            # If you need to look ahead for multi-character normalization rules
            # chunk = original_text[i:i+n]  # where n is how far you need to look ahead
            
            # Apply your normalization rules
            normalized_chunk = self.__normalize_heading(chunk)
            
            # Add normalized characters and their position mappings
            for norm_char in normalized_chunk:
                normalized.append(norm_char)
                position_map.append(i)
            
            i += 1
        
        return ''.join(normalized), position_map
    
    def split_text_by_headings(self, text, headings):
        """
        Split the text into sections based on the headings.
        Args:
            text (str): The text to split.
            headings (list): The list of headings.
        Returns:
            list: The list of sections.
        """
        if not self.__headings_are_sequential(text, headings):
            print("error, nonsequential headings")
            headings = self.get_sequential_headings(text, headings)
        
        sections = {}
        last_index = 0
        skip_next_match = False  # To handle duplicate headings inside Index
        first_heading_found = False  # Flag to track if we've found the first heading
        
        for i, heading in enumerate(headings):
            normalized_heading = self.__normalize_heading(heading)
            normalized_text, position_map = self.__create_position_mapping(text[last_index:])
            matches = list(re.finditer(rf'\b{re.escape(normalized_heading)}\b', normalized_text))
            if not matches:
                continue  # Skip if no match found
            
            # Handle Index heading special case
            if "index" in heading.lower() and i == 0:
                next_heading = headings[i + 1] if i + 1 < len(headings) else None
                if next_heading:
                    normalized_next_heading = self.__normalize_heading(next_heading)
                    next_matches = list(re.finditer(rf'\b{re.escape(normalized_next_heading)}\b', normalized_text))
                    
                    if len(next_matches) > 1:
                        match = next_matches[1]  # Take the second occurrence
                    else:
                        match = next_matches[0] if next_matches else None
                else:
                    match = matches[0]  # If Index is the only heading, take its match
            else:
                # If we're in a normal section, pick the correct heading match
                match = matches[1] if skip_next_match and len(matches) > 1 else matches[0]
            
            skip_next_match = ("index" in heading.lower())  # Enable skipping for next heading after Index
            
            if match:
                original_start_index = position_map[match.start()]
                start_index = last_index + original_start_index
                
                # Save the previous section
                if first_heading_found:
                    section_content = text[last_index:start_index].strip()
                    if section_content:
                        sections[headings[i-1]] = section_content
                else:
                    # This is the first heading found - save any content before it as "Introduction" or similar
                    intro_content = text[:start_index].strip()
                    if intro_content:
                        sections["Introduction"] = intro_content
                    first_heading_found = True
                    
                last_index = start_index  # Move to new position
        
        # Capture the last section
        sections[headings[-1]] = text[last_index:].strip()
        
        return list(sections.values())


    def __format_azure_result(self, result):
        full_content = []

        # Parse document sections
        for section in result.sections:
            for element_ref in section.elements:
                if element_ref.startswith("/paragraphs/"):
                    # Extract paragraph content
                    paragraph_idx = int(element_ref.split("/")[-1])
                    paragraph = result.paragraphs[paragraph_idx]
                    full_content.append(paragraph.content + "\n")

                elif element_ref.startswith("/tables/"):
                    # Extract table content
                    table_idx = int(element_ref.split("/")[-1])
                    table = result.tables[table_idx]

                    table_data = []
                    for cell in table.cells:
                        table_data.append({
                            "row": cell.row_index,
                            "column": cell.column_index,
                            "content": cell.content
                        })

                    df = pd.DataFrame(table_data)
                    df_pivot = df.pivot(index="row", columns="column", values="content").fillna('')
                    table_str = self.formatDataFrame("md",df_pivot.reset_index(drop=True))
                    
                    full_content.append("\n" + table_str + "\n")
        # Save formatted document content
        
        full_content_str = "\n".join(full_content)
        #with open("azure_testing/FormattedDocument.md", "w") as f:
        #    f.write(full_content_str)
        return full_content_str

    def ocr_pdf_azure(self,pdf_path):
        """
        Process large PDF files by breaking them into smaller chunks
        and converting each chunk to markdown before combining results.
        
        Parameters:
        path_to_file (str): Path to the PDF file
        
        Returns:
        str: Combined markdown string of the entire PDF
        """
        chunk_size = 2
        
        # Read the PDF length
        pdf = PdfReader(pdf_path)
        total_pages = len(pdf.pages)
    
        
        if total_pages <= chunk_size: return self.__ocr_pdf_azure_2pg(pdf_path)

        # Create a temporary directory for chunk files
        temp_dir = "temp_pdf_chunks"
        os.makedirs(temp_dir, exist_ok=True)

        # Break the PDF into chunks (2 pages per chunk)
        markdown_chunks = []
        
        for i in range(0, total_pages, chunk_size):
            # Create a PDF writer for this chunk
            pdf_writer = PdfWriter()
            
            # Add pages to the chunk (up to chunk_size or remaining pages)
            end_page = min(i + chunk_size, total_pages)
            for page_num in range(i, end_page):
                pdf_writer.add_page(pdf.pages[page_num])
            
            # Save the chunk to a temporary file
            chunk_path = os.path.join(temp_dir, f"chunk_{i//chunk_size}.pdf")
            with open(chunk_path, "wb") as chunk_file:
                pdf_writer.write(chunk_file)
            
            # Convert the chunk to markdown and append to results
            markdown_chunk = self.__ocr_pdf_azure_2pg(chunk_path)
            if markdown_chunk:
                markdown_chunks.append(markdown_chunk)
            else:
                print("no markdown chunk for", chunk_path)
    
        full_markdown = self.__fix_split_tables(markdown_chunks)
        
        # Clean up temporary files
        for file in os.listdir(temp_dir):
            os.remove(os.path.join(temp_dir, file))
        os.rmdir(temp_dir)
        
        return full_markdown
        
    def __ocr_pdf_azure_2pg(self, pdf_path):
        document_intelligence_client  = DocumentIntelligenceClient(
            endpoint=azure_endpoint, credential=AzureKeyCredential(azure_key)
        )
        
        with open(pdf_path, "rb") as f:
            poller = document_intelligence_client.begin_analyze_document(
                "prebuilt-layout", AnalyzeDocumentRequest(bytes_source=f.read()) #prebuilt-read for basic read, prebuilt-layout for layout
            )
        result = poller.result()
        print(f"Used Azure to OCR pdf with {len(result.pages)} pages")

        return self.__format_azure_result(result)

        
    def __fix_split_tables(self, markdown_chunks):
        """
        Fix split tables across chunks by detecting table endings and beginnings
        
        Parameters:
        markdown_chunks (list): List of markdown strings
        
        Returns:
        str: Combined markdown with fixed tables
        """
        # Initialize the result
        fixed_chunks = []
        
        for i in range(len(markdown_chunks)):
            current_chunk = markdown_chunks[i]
            
            # Skip if this is the last chunk (no next chunk to merge with)
            if i == len(markdown_chunks) - 1:
                fixed_chunks.append(current_chunk)
                continue
            
            next_chunk = markdown_chunks[i + 1]
            
            # Check if the current chunk ends with a potentially incomplete table
            ends_with_table = re.search(r'<\/tr>\s*<\/table>\s*$', current_chunk.strip())
            
            # Check if the next chunk starts with a potentially incomplete table
            starts_with_table = re.search(r'^.*?<table>.*?<tr>', next_chunk.strip(), re.DOTALL)
            
            if ends_with_table and starts_with_table:
                # Count columns in the ending table
                table_end_content = re.findall(r'<tr>.*?<\/tr>', current_chunk.split('</table>')[-2], re.DOTALL)
                if table_end_content:
                    last_row = table_end_content[-1]
                    end_columns = len(re.findall(r'<td>.*?<\/td>', last_row)) or len(re.findall(r'<th>.*?<\/th>', last_row))
                else:
                    end_columns = 0
                    
                # Count columns in the starting table
                table_start_content = re.findall(r'<tr>.*?<\/tr>', next_chunk.split('<table>')[1], re.DOTALL)
                if table_start_content:
                    first_row = table_start_content[0]
                    start_columns = len(re.findall(r'<td>.*?<\/td>', first_row)) or len(re.findall(r'<th>.*?<\/th>', first_row))
                else:
                    start_columns = 0
                
                # If the column counts match, merge the tables
                if end_columns > 0 and end_columns == start_columns:
                    # Extract the ending table without its closing tag
                    end_table_parts = current_chunk.rsplit('</table>', 1)
                    merged_chunk = end_table_parts[0]
                    
                    # Extract the content of the starting table without its opening tag
                    start_table_parts = next_chunk.split('<table>', 1)
                    if len(start_table_parts) > 1:
                        table_content = start_table_parts[1].split('</table>', 1)
                        
                        # Merge tables and add the rest of the content
                        merged_chunk += table_content[0] + '</table>'
                        if len(table_content) > 1:
                            merged_chunk += table_content[1]
                        
                        # Replace current chunk with merged chunk
                        fixed_chunks.append(merged_chunk)
                        
                        # Skip the next chunk since we've merged it
                        markdown_chunks[i + 1] = ''
                        continue
            
            # If no merge happened, add the current chunk as is
            fixed_chunks.append(current_chunk)
        
        # Filter out any empty chunks and combine
        return "\n\n".join([chunk for chunk in fixed_chunks if chunk])


    def excel_to_csv(self, excel_file, csv_file=None, sheet_name=0, index=False, encoding='utf-8'):
        """
        Convert an Excel file to a CSV file using pandas.
        
        Parameters:
        - excel_file (str): Path to the input Excel file (.xlsx, .xls)
        - csv_file (str, optional): Path to the output CSV file. If None, uses the same name as Excel file with .csv extension
        - sheet_name (str/int, optional): Name or index of the sheet to convert (default: first sheet)
        - index (bool): Whether to write row names (index) to the CSV (default: False)
        - encoding (str): Encoding to use for the CSV file (default: 'utf-8')
        
        Returns:
        - str: Path to the created CSV file
        
        Example:
        excel_to_csv('input.xlsx', 'output.csv')
        """
        try:
            # Read the Excel file
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            # Determine output CSV filename if not provided
            if csv_file is None:
                if excel_file.endswith('.xlsx'):
                    csv_file = excel_file.replace('.xlsx', '.csv')
                elif excel_file.endswith('.xls'):
                    csv_file = excel_file.replace('.xls', '.csv')
                else:
                    csv_file = excel_file + '.csv'
            
            # Write to CSV
            df.to_csv(csv_file, index=index, encoding=encoding)
            
            print(f"Successfully converted '{excel_file}' to '{csv_file}'")
            return csv_file
        
        except Exception as e:
            print(f"Error converting Excel to CSV: {str(e)}")
            raise

#     def ocr_pdf_mistral(self, pdf_path):

#         def encode_pdf(pdf_path):
#             """Encode the pdf to base64."""
#             try:
#                 with open(pdf_path, "rb") as pdf_file:
#                     return base64.b64encode(pdf_file.read()).decode('utf-8')
#             except FileNotFoundError:
#                 print(f"Error: The file {pdf_path} was not found.")
#                 return None
#             except Exception as e:  # Added general exception handling
#                 print(f"Error: {e}")
#                 return None
#         # Getting the base64 string
#         base64_pdf = encode_pdf(pdf_path)

#         api_key = os.getenv("MISTRAL_API_KEY")
#         client = Mistral(api_key=api_key)

#         ocr_response = client.ocr.process(
#             model="mistral-ocr-latest",
#             document={
#                 "type": "document_url",
#                 "document_url": f"data:application/pdf;base64,{base64_pdf}" 
#             },
#             include_image_base64=True
#         )
#         #print(ocr_response)
#         ocr_text = ""
#         for page in ocr_response.pages:
#             ocr_text += f"Page {page.index}:\n{page.markdown}\n\n"
#         return ocr_text

# if __name__ == "__main__":
#     doc_processor = Doc_Processor()
#     resp = doc_processor.ocr_pdf_mistral("C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/test_data/Addo_Elephant_1Page.pdf")
#     with open("C:/Users/<USER>/OneDrive/Documents/Personal Training/Windsurfer_Test_Service_Docs/service_doc_processor_TDM/backend/test_data/Addo_Elephant_1Page_ocr.md", "w") as f:
#         f.write(resp)
#     print(resp)