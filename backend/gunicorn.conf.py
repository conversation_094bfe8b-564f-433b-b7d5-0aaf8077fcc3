# Gunicorn configuration file for production

import os

# Server socket
bind = "0.0.0.0:6060"
backlog = 2048

# Worker processes
workers = int(os.getenv('WORKERS', 4))
worker_class = "sync"
worker_connections = 1000
timeout = 300
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 100

# Logging
accesslog = "-"  # Log to stdout
errorlog = "-"   # Log to stderr
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = 'tourvest_doc_processor'

# Server mechanics
daemon = False
pidfile = None
user = None
group = None
tmp_upload_dir = None

# SSL (if needed in future)
# keyfile = None
# certfile = None

# Application - this is not used when module is specified in command line
# module = "ProcessorAPI:app"

# Development settings (commented out for production)
# reload = True
# reload_extra_files = []
