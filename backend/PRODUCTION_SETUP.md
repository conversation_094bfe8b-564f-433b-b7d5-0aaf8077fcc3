# Backend Production Setup

This document describes the production configuration for the Tourvest Document Processor Backend.

## Production vs Development Configuration

### Environment Variables

The application behavior is controlled by the `FLASK_ENV` environment variable:

- **Production**: `FLASK_ENV=production`
- **Development**: `FLASK_ENV=development` (default)

### Key Differences

| Feature | Production | Development |
|---------|------------|-------------|
| WSGI Server | Gunicorn | Flask dev server |
| Debug Mode | Disabled | Enabled |
| Logging Level | INFO | DEBUG |
| CORS Origins | Restricted to production domains | Localhost + production |
| Error Messages | Generic | Detailed |
| Workers | 4 (configurable) | 1 |

## Production Configuration

### Docker Compose

The `docker-compose.yml` is configured for production with:

```yaml
environment:
  - FLASK_ENV=production
  - FLASK_APP=ProcessorAPI.py
  - FLASK_DEBUG=0
  - WORKERS=4
```

### Gunicorn Configuration

Production uses Gunicorn with the following settings:
- 4 workers (configurable via `WORKERS` env var)
- 300-second timeout
- Request limits to prevent memory leaks
- Proper logging configuration

### CORS Configuration

Production CORS is restricted to:
- `https://tourvest.darcartz.com`
- `https://tdm_contract_processor.sapconet.org`

### Security Features

- Non-root user in Docker container
- Generic error messages in production
- Proper logging configuration
- Request limits and timeouts

## Switching Between Environments

### For Production (Current Setup)
```bash
docker-compose up --build
```

### For Development
1. Edit `docker-compose.yml` and uncomment development environment variables:
```yaml
environment:
  # Production configuration (commented out)
  # - FLASK_ENV=production
  # - FLASK_APP=ProcessorAPI.py
  # - FLASK_DEBUG=0
  # - WORKERS=4
  # Development configuration
  - FLASK_ENV=development
  - FLASK_APP=ProcessorAPI.py
  - FLASK_DEBUG=1
```

2. Rebuild and run:
```bash
docker-compose up --build
```

## Health Check

The application provides a health check endpoint at `/health` for monitoring:

```bash
curl http://localhost:6060/health
```

## Monitoring and Logs

- Application logs are sent to stdout/stderr
- Gunicorn access logs include timing information
- Health check endpoint for monitoring systems
- Structured logging in production

## Files Modified for Production

1. `docker-compose.yml` - Environment variables
2. `ProcessorAPI.py` - Environment-based configuration
3. `Dockerfile` - Production optimizations
4. `requirements.txt` - Added Gunicorn
5. `gunicorn.conf.py` - Gunicorn configuration
6. `start.sh` - Startup script
7. `PRODUCTION_SETUP.md` - This documentation
