import pytest
import json
from unittest.mock import patch
# Assuming your Flask app instance is created in backend.ProcessorAPI and named 'app'
# Adjust the import path as necessary if your app factory or instance is elsewhere.
from backend.ProcessorAPI import app as flask_app

@pytest.fixture
def app():
    # Configure the app for testing
    flask_app.config.update({
        "TESTING": True,
    })
    # You might need to initialize extensions or blueprints if they aren't already
    # For example, if PrometheusMetrics is not initialized when app is imported:
    # from prometheus_flask_exporter import PrometheusMetrics
    # if not hasattr(flask_app, 'extensions') or 'prometheus_metrics' not in flask_app.extensions:
    #     PrometheusMetrics(flask_app) # This might cause issues if already initialized.
                                     # It's better if app initialization handles this.
                                     # For this test, we assume ProcessorAPI.py correctly sets up metrics.

    # Ensure the metrics are registered for the test client
    # This might not be strictly necessary if already handled by app setup
    # but ensures the test environment is consistent.
    with flask_app.app_context():
        pass # Ensures app context is active for any on-demand setup

    yield flask_app

@pytest.fixture
def client(app):
    return app.test_client()

def test_metrics_endpoint(client):
    """Test that the /metrics endpoint is reachable and returns expected metric names."""
    response = client.get('/metrics')
    assert response.status_code == 200
    response_text = response.get_data(as_text=True)

    # Check for some of the metric names defined in ProcessorAPI.py
    # These are default names, actual names might have _total or other suffixes by default
    assert 'processed_documents_total' in response_text
    assert 'file_uploads_total' in response_text
    assert 'auto_process_tasks_total' in response_text
    assert 'document_processing_errors_total' in response_text
    # Check for a standard metric provided by prometheus_flask_exporter
    assert 'flask_http_request_duration_seconds' in response_text

@patch('backend.ProcessorAPI.app.logger.info') # Mock the app's logger
def test_log_frontend_event_success(mock_logger_info, client):
    """Test the /log-frontend-event endpoint with valid data."""
    event_data = {
        "event_type": "click",
        "component": "TestButton",
        "message": "User clicked test button",
        "details": {"button_id": "test-btn"}
    }
    response = client.post('/log-frontend-event', json=event_data)
    assert response.status_code == 200
    assert response.json['status'] == 'success'

    # Check if logger.info was called and verify part of the log message
    mock_logger_info.assert_called_once()
    args, _ = mock_logger_info.call_args
    log_message = args[0]
    assert "[FRONTEND_EVENT]" in log_message
    assert "Type: click" in log_message
    assert "Component: TestButton" in log_message
    assert "Message: User clicked test button" in log_message
    assert "Details: {'button_id': 'test-btn'}" in log_message

def test_log_frontend_event_invalid_data(client):
    """Test the /log-frontend-event endpoint with no data."""
    # Test with empty JSON object
    response_empty_json = client.post('/log-frontend-event', json={})
    assert response_empty_json.status_code == 400
    assert response_empty_json.json['error'] == 'No data provided'

    # Test with no JSON data (malformed request from client perspective)
    response_no_data = client.post('/log-frontend-event', data='not json')
    # Flask's default behavior for non-JSON data to get_json(silent=False) would be a 400.
    # If get_json(silent=True) or get_json(force=True) is used with error handling,
    # it might differ. The current implementation in ProcessorAPI for these
    # endpoints uses request.get_json() which implies silent=False by default if
    # Content-Type is application/json, but if Content-Type is wrong, it's None.
    # The added `if not data:` check handles when data is None (e.g. no json or wrong content-type)
    assert response_no_data.status_code == 400
    assert response_no_data.json['error'] == 'No data provided'


@patch('backend.ProcessorAPI.app.logger.error') # Mock the app's error logger
def test_log_frontend_error_success(mock_logger_error, client):
    """Test the /log-frontend-error endpoint with valid data."""
    error_data = {
        "error_message": "Test frontend error",
        "component": "TestComponent",
        "details": {"stack_trace": "...", "user_agent": "pytest"}
    }
    response = client.post('/log-frontend-error', json=error_data)
    assert response.status_code == 200
    assert response.json['status'] == 'success'

    mock_logger_error.assert_called_once()
    args, _ = mock_logger_error.call_args
    log_message = args[0]
    assert "[FRONTEND_ERROR]" in log_message
    assert "Message: Test frontend error" in log_message
    assert "Component: TestComponent" in log_message
    assert "Details: {'stack_trace': '...', 'user_agent': 'pytest'}" in log_message

def test_log_frontend_error_invalid_data(client):
    """Test the /log-frontend-error endpoint with no data."""
    # Test with empty JSON object
    response_empty_json = client.post('/log-frontend-error', json={})
    assert response_empty_json.status_code == 400
    assert response_empty_json.json['error'] == 'No data provided'

    # Test with no JSON data
    response_no_data = client.post('/log-frontend-error', data='not json')
    assert response_no_data.status_code == 400
    assert response_no_data.json['error'] == 'No data provided'

# Instructions for running the tests:
# 1. Ensure pytest and pytest-mock (if needed for more complex scenarios, though patch is from unittest.mock) are installed:
#    pip install pytest pytest-mock
# 2. Navigate to the root directory of the project.
# 3. Run pytest:
#    pytest
#    or specifically for this file:
#    pytest backend/tests/test_logging_metrics_api.py
#
# Create an empty backend/tests/__init__.py if it doesn't exist to make 'tests' a package.
