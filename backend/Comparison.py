import re
import pandas as pd
import textwrap
from itertools import zip_longest
import os

#-----------------------STANDARDIZE DATE FORMAT----------------------------

#Detects what format the received date is
def detect_date_format(date_str):
    """Detect if date is in DD/MM/YYYY or YYYY-MM-DD."""
    if re.match(r"\d{4}-\d{2}-\d{2}", date_str):  # Matches YYYY-MM-DD
        return "%Y-%m-%d"
    elif re.match(r"\d{2}/\d{2}/\d{4}", date_str):  # Matches DD/MM/YYYY
        return "%d/%m/%Y"
    return None 

#Converts the date to %Y-%m-%d format for comparison
def convert_date(value, target_format="%Y-%m-%d"):
    try:
        detected_format = detect_date_format(value)
        if detected_format:
            parsed_date = pd.to_datetime(value, format=detected_format)
            return parsed_date.strftime(target_format)
        else:
            return value 
    except Exception:
        return value  

#Applies the conversion to the df
def standardize_dates(df, date_columns, target_format="%Y-%m-%d"):
    for col in date_columns:
        df[col] = df[col].apply(lambda x: convert_date(x, target_format) if pd.notna(x) else x)
    return df

#----------------------CONVERT NUMBERS TO INT/FLOAT---------------------------

#Converts numeric columns to integers/floats while skipping date columns.
import pandas as pd

def convert_numerics(df, date_columns):
    for col in df.columns:
        if col not in date_columns:
            try:
                df[col] = pd.to_numeric(df[col]) 
            except (ValueError, TypeError):  
                pass  # Skip columns that can't be converted
    return df

#----------------------COMPUTE ROW AND COL RECALL-------------------------------

def row_and_column_recall(ground_truth_csv, system_output_csv):
    #Load CSV files
    ground_truth_df = pd.read_csv(ground_truth_csv).astype(str).apply(lambda x: x.str.lower().str.strip(), axis=1)
    system_output_df = pd.read_csv(system_output_csv).astype(str).apply(lambda x: x.str.lower().str.strip(), axis=1)

    #Standardize date format
    date_columns = [col for col in ground_truth_df.columns if "date" in col.lower()]
    ground_truth_df = standardize_dates(ground_truth_df, date_columns)
    system_output_df = standardize_dates(system_output_df, date_columns)
    ground_truth_df[date_columns] = ground_truth_df[date_columns].astype(str) # Convert dates to string again for comparison
    system_output_df[date_columns] = system_output_df[date_columns].astype(str)

    #Numeric conversion
    ground_truth_df = convert_numerics(ground_truth_df, date_columns)
    system_output_df = convert_numerics(system_output_df, date_columns)

    #Keeps unique row items
    ground_truth_set = set(ground_truth_df.itertuples(index=False, name=None))
    extracted_set = set(system_output_df.itertuples(index=False, name=None))

    # Compute row-level recall
    correct_rows = ground_truth_set & extracted_set
    missing_rows = ground_truth_set - extracted_set  
    extra_rows = extracted_set - ground_truth_set    
    tp_rows = len(ground_truth_set & extracted_set)
    fn_rows = len(missing_rows)
    recall_rows = tp_rows / (tp_rows + fn_rows) if (tp_rows + fn_rows) > 0 else 0

    # Compute column-wise recall
    recall_per_column = {}
    missing_per_column = {}
    extra_per_column = {}
    for column in ground_truth_df.columns:
        ground_col = set(ground_truth_df[column])  
        extracted_col = set(system_output_df[column])

        missing_col = ground_col - extracted_col  
        extra_col = extracted_col - ground_col   

        tp_col = len(ground_col & extracted_col)  
        fn_col = len(missing_col)  
        
        recall_col = tp_col / (tp_col + fn_col) if (tp_col + fn_col) > 0 else 0
        recall_per_column[column] = recall_col
        missing_per_column[column] = missing_col
        extra_per_column[column] = extra_col

    #Create debug report
    input_file_name = str(os.path.basename(system_output_csv))
    output_file_name = input_file_name[:input_file_name.rfind('.')] + "_report.txt"
    with open(output_file_name, "w") as f:
        f.write("===== Debug Report =====\n\n")
        
        # Row-level results
        f.write(f"Total ground truth rows: {len(ground_truth_set)}\n")
        f.write(f"Total system output rows: {len(extracted_set)}\n")
        f.write(f"Correctly extracted rows: {len(correct_rows)}\n")
        f.write(f"Missing rows: {len(missing_rows)}\n")
        f.write(f"Extra rows: {len(extra_rows)}\n\n")

        f.write("===== Column-Wise Differences =====\n\n")
        f.write("**Column-wise Recall:**\n")
        for col, rec in recall_per_column.items():
            f.write(f"{col}: {rec:.2%}\n")

        f.write("\n**Missing & Extra Values Per Column:**\n")
        for col in ground_truth_df.columns:
            missing_values = missing_per_column.get(col, [])
            extra_values = extra_per_column.get(col, [])

            col_width1 = 50  
            col_width2 = 50  
            separator = " | "  

            f.write("\n**Missing & Extra Values Per Column:**\n")

            for col in ground_truth_df.columns:
                missing_values = missing_per_column.get(col, [])
                extra_values = extra_per_column.get(col, [])

                if not missing_values and not extra_values:
                    continue  

                f.write(f"\n{col}:\n")
                f.write(f"{'Missing Values':<{col_width1}}{separator}{'Extra Values':<{col_width2}}\n")
                f.write("-" * (col_width1 + col_width2 + len(separator)) + "\n")

                # Iterate using zip_longest to avoid manual padding
                for m, e in zip_longest(missing_values, extra_values, fillvalue=""):
                    m_wrapped = textwrap.wrap(str(m), width=col_width1)
                    e_wrapped = textwrap.wrap(str(e), width=col_width2)

                    # Ensure both lists have the same number of wrapped lines
                    max_lines = max(len(m_wrapped), len(e_wrapped))
                    m_wrapped += [""] * (max_lines - len(m_wrapped))
                    e_wrapped += [""] * (max_lines - len(e_wrapped))

                    for mw, ew in zip(m_wrapped, e_wrapped):
                        f.write(f"{mw:<{col_width1}}{separator}{ew:<{col_width2}}\n")

    print(f"Wrote debug report to {output_file_name}")
    return recall_rows, correct_rows, recall_per_column, missing_rows, extra_rows

# Example usage
row_and_column_recall("ground_truths/ground_truth_addo_Sheet1.csv", "addo_from_normal_read_pdf.csv")

