import asyncio
from helpers.friends import MicroService
from helpers.Document_Processing import Doc_Processor
from general_document_parser import general_document_parser
from property_parser import property_parser
from helpers.LLM_Handler import LLM_Handler
import logging
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(f"Doc_Processor_Microservice")
#-----------------------------------------------------------------------Doc processor service methods-----------------------------------------------------------------------

#--------helpers--------
def get_temp_file_path(filename):
    cwd = os.getcwd()
    return f"temp/{filename}" if cwd.endswith("backend") else f"backend/temp/{filename}"

def get_doc_string(filename):
    try:
        file_path = get_temp_file_path(filename)
        if filename.endswith('.txt'):  # For chunk files
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:  # For PDF files
            return Doc_Processor().read_pdf_to_string(file_path)
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        raise e

def process_document_general(filename, model="gemini-2.0-flash"):
    try:
        pdfstr = get_doc_string(filename)
        return general_document_parser(pdfstr, model=model)
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        raise e
    
def process_document_property(filename, property_name, model="gemini-2.0-flash"):
    try:
        pdfstr = get_doc_string(filename)
        return property_parser(pdfstr, property_name, model=model)
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        raise e

def return_Document_Chunks(filename, model="gemini-2.0-flash", length_threshold=5000):
    try:
        pdfstr = get_doc_string(filename)
        llm_handler = LLM_Handler(model)
        headings, sections = Doc_Processor().chunk_doc_if_needed(pdfstr, llm_handler, length_threshold=length_threshold)
        return {'headings': headings, 'sections': sections}
    except Exception as e:
        logger.error(f"Error processing document: {str(e)}")
        raise e
    
#--------exposed methods--------

async def get_valid(filename):
    """
    Asynchronously validates a document by its filename.
    Args:
        filename (str): The name of the file to be validated.
    Returns:
        dict: A dictionary containing either the validation result or an error message.
        - If the file is not found, returns {'error': 'File not found: <file_path>'}.
        - If the document is successfully processed, returns {'valid': <validation_result>}.
        - If an error occurs during processing, returns {'error': 'Processing error: <error_message>'}.
        - If an unexpected error occurs, returns {'error': '<error_message>'}.
    """
    try:
        
        file_path = get_temp_file_path(filename)
        if not os.path.exists(file_path):
            return {'error': f'File not found: {file_path}'}
        
        logger.info(f"Processing document: {filename}")    
        try:
            parser = process_document_general(filename)
            return {'valid': parser.get_valid()}
        except Exception as e:
            logger.error(f"Error in get_valid: {str(e)}")
            return {'error': f'Processing error: {str(e)}'}
    
    except Exception as e:
        logger.error(f"Unexpected error in get_valid: {str(e)}")
        return {'error': str(e)}

async def get_property_names(filename):
    """
    Asynchronously retrieves property names from a given document file.
    Args:
        filename (str): The name of the file to process.
    Returns:
        dict: A dictionary containing either the property names or an error message.
            - If successful, returns {'property_names': list_of_property_names}.
            - If the file is not found, returns {'error': 'File not found: temp/filename'}.
            - If an exception occurs, returns {'error': str(exception)}.
    """
    try:
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': f'File not found: {file_path}'}
        
            
        parser = process_document_general(filename)
        return {'property_names': parser.get_property_names()}
    
    except Exception as e:
        return {'error': str(e)}

# Endpoint to get the overarching period
async def get_overarching_period(filename):
    """
    Asynchronously retrieves the overarching period from a document.
    Args:
        filename (str): The name of the file to process.
    Returns:
        dict: A dictionary containing the start and end dates of the overarching period, 
              or an error message if the file is not found or an exception occurs.
              Example:
              {
                  'start_date': 'YYYY-MM-DD',
                  'end_date': 'YYYY-MM-DD'
              }
              or
              {
                  'error': 'File not found'
              }
              or
              {
                  'error': 'Exception message'
              }
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
            
        parser = process_document_general(filename)
        period = parser.get_overarching_period()
        return {'start_date': period[0], 'end_date': period[1]}
    
    except Exception as e:
        return {'error': str(e)}

# Endpoint to get all periods
async def get_periods(filename):
    """
    Asynchronously retrieves periods from a document.
    Args:
        filename (str): The name of the file to process.
    Returns:
        dict: A dictionary containing either the periods extracted from the document
              or an error message if the file is not found or an exception occurs.
    Raises:
        Exception: If an error occurs during the processing of the document.
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
            
        parser = process_document_general(filename)
        return {'periods': parser.get_periods()}
    
    except Exception as e:
        return {'error': str(e)}

# Endpoint to check if the document contains STO information
async def general_has_sto(filename):
    """
    Check if the given hotel contains STO rates
    Args:
        filename (str): The name of the file to be processed.
    Returns:
        dict: A dictionary containing either:
            - {'has_sto': bool}: If the file is found and processed successfully, indicating whether the document contains STO rates
            - {'error': str}: If an error occurs, containing the error message.
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
            
        parser = process_document_general(filename)
        return {'has_sto': parser.general_has_sto()}
    
    except Exception as e:
        return {'error': str(e)}

#------------------Property Specific Routes------------------

# Endpoint to get includes

async def property_includes(filename, property_name):
    """
    Asynchronously processes a document to retrieve inclusions at a property
    Args:
        filename (str): The name of the file to be processed.
        property_name (str): The name of the property to check for inclusions.
    Returns:
        dict: A dictionary containing either the inclusions or an error message.
              Example: {'excludes': [...] } or {'error': '...'}
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        parser = process_document_property(filename,property_name)
        return {'includes': parser.get_includes()}
    
    except Exception as e:
        return {'error': str(e)}
    
# Endpoint to get excludes
async def property_excludes(filename, property_name):
    """
    Asynchronously processes a document to retrieve exclusions at a property
    Args:
        filename (str): The name of the file to be processed.
        property_name (str): The name of the property to check for exclusions.
    Returns:
        dict: A dictionary containing either the exclusions or an error message.
              Example: {'excludes': [...] } or {'error': '...'}
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        parser = process_document_property(filename,property_name)
        return {'excludes': parser.get_excludes()}
    
    except Exception as e:
        return {'error': str(e)}

# Endpoint to validate property
async def validate_property(filename, property_name):
    """
    Validate a specific property of a document.
    Args:
        filename (str): The name of the file to be validated.
        property_name (str): The name of the property to validate within the document.
    Returns:
        dict: A dictionary containing the validation result. If the file is not found, 
              returns {'error': 'File not found'}. If an exception occurs, returns 
              {'error': str(e)}. Otherwise, returns {'valid': parser.validate_property()}.
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        parser = process_document_property(filename,property_name)
        return {'valid': parser.validate_property()}
    
    except Exception as e:
        return {'error': str(e)}

# Endpoint to get room types

async def property_room_types(filename, property_name):
    """
    Extracts room types from a document based on the given property name.
    Args:
        filename (str): The name of the file to process.
        property_name (str): The property name to extract room types for.
    Returns:
        dict: A dictionary containing either the room types or an error message.
            - If successful, returns {'room_types': list_of_room_types}.
            - If the file is not found, returns {'error': 'File not found'}.
            - If an exception occurs, returns {'error': str(exception)}.
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        parser = process_document_property(filename,property_name)
        return {'room_types': parser.get_room_types()}
    
    except Exception as e:
        return {'error': str(e)}

# Endpoint to get meal types  
async def property_meal_types(filename, property_name):
    """
    Extracts meal types from a document based on the given property name.
    Args:
        filename (str): The name of the file to process.
        property_name (str): The property name to extract meal types from.
    Returns:
        dict: A dictionary containing either the meal types or an error message.
            - If successful, returns {'meal_types': list_of_meal_types}.
            - If the file is not found, returns {'error': 'File not found'}.
            - If an exception occurs, returns {'error': str(exception)}.
    """
    try:
        
        file_path = get_temp_file_path(filename)
        
        if not os.path.exists(file_path):
            return {'error': 'File not found'}
        
        parser = process_document_property(filename,property_name)
        return {'meal_types': parser.get_meal_types()}
    
    except Exception as e:
        return {'error': str(e)}


#-----------------------------------------------------------------------main-----------------------------------------------------------------------
class doc_processor_microservice:
    def __init__(self):
        self.doc_processor_service = MicroService("doc_processor_service")

    async def start(self):
        self.doc_processor_service.register_command("get_valid", get_valid)
        self.doc_processor_service.register_command("get_property_names", get_property_names)
        self.doc_processor_service.register_command("get_overarching_period", get_overarching_period)
        self.doc_processor_service.register_command("get_periods", get_periods)
        self.doc_processor_service.register_command("general_has_sto", general_has_sto)
        self.doc_processor_service.register_command("property_includes", property_includes)
        self.doc_processor_service.register_command("property_excludes", property_excludes)
        self.doc_processor_service.register_command("validate_property", validate_property)
        self.doc_processor_service.register_command("property_room_types", property_room_types)
        self.doc_processor_service.register_command("property_meal_types", property_meal_types)
    
        # Start the service
        await self.doc_processor_service.start()
        
    async def stop(self):
        await self.doc_processor_service.stop()


async def main():
    service = doc_processor_microservice()
    await service.start()
    print("doc processor service started! type 'stop' to stop")
    while input() != "stop":
        pass
    
    service.stop()
    

if __name__ == "__main__":
    asyncio.run(main())
