

import re
import json
from datetime import datetime, timedelta
import csv
from openpyxl import load_workbook
from room_type_parser import room_type_parser
from helpers.LLM_Handler import LLM_Handler
from helpers.Document_Processing import Doc_Processor
from general_document_parser import general_document_parser
from property_parser import property_parser

openAIModels = ["gpt-4o-mini","gpt-4o"]
geminiModels = ["gemini-2.0-flash"]

#---------------------------------------------------------llm functions---------------------------------------------------------


def is_within_range(validity_start: str, validity_end: str, dates: list[str]) -> list[str] | str:
    """
    Identifies any dates that are not within the valid period.

    Args:
        validity_start : The starting date for the period of validity, in YYYY-MM-DD format.
        validity_end : The end date for the period of validity, in YYYY-MM-DD format.
        dates : A list of dates to validate, where each date is in YYYY-MM-DD format.

    Returns:
    --------
        A list of dates that are not within the valid period. Each date is in YYYY-MM-DD format.
    """
    # Convert the validity_start and validity_end strings to datetime objects
    try:
        start_date = datetime.strptime(validity_start, '%Y-%m-%d')
        end_date = datetime.strptime(validity_end, '%Y-%m-%d')
    
        
        invalid_dates = []
        
        for date_str in dates:
            # Convert each date string to a datetime object
            date = datetime.strptime(date_str, '%Y-%m-%d')
            
            # Check if the date is outside the validity range
            if date < start_date or date > end_date:
                invalid_dates.append(date_str)
        
        if not invalid_dates:
            return "All dates are valid."
        else:
            return f"The following dates are not valid: {', '.join(invalid_dates)}"
        
    except ValueError:
        return f"Invalid date format. Please use YYYY-MM-DD."

#---------------------------------------------------------document processor---------------------------------------------------------

class document_processor:

    def __init__(self, model="gpt-4o-mini"):
        """
        Initialize the document processor with the specified model.

        Args:
            model (str): The model to use for processing. Default is "gpt-4o-mini".
        """
        if not (model in openAIModels or model in geminiModels):
            raise Exception(f"Error - unknown model: {model}")
        self.model = model
        self.llm_handler = LLM_Handler(model)
        self.document_processor = Doc_Processor()
       

    #------------------------------------------------Helpers and Tools------------------------------------------------
   
    
    def fixCurrency(self, input_string):
        """
        Fix the currency format in a string.

        Args:
            input_string (str): The input string containing the currency.

        Returns:
            float: The fixed currency value.
        """
        # Use regex to find all digits and optional decimal points
        number_match = re.search(r'-?\d*\.?\d+', input_string)
        
        if number_match:
            # Extract the matched number
            number_str = number_match.group(0)
        
        return float(number_str)
    
    def sum_room_rates(self, yamlresponse):
        """
        Sum the room rates from a YAML response.

        Args:
            yamlresponse (dict): The YAML response containing room rates.

        Returns:
            float: The sum of the room rates.
        """
        result = self.fixCurrency(str(yamlresponse["BASE_RATE"])) if yamlresponse["BASE_RATE"] else 0
        if yamlresponse["ADDITIONAL_FEES"]:
            for fee in yamlresponse["ADDITIONAL_FEES"]:
                result += self.fixCurrency(str(fee))
        return result

    def merge_date_ranges(self, date_ranges):
        """
        Merge overlapping or continuous date ranges.

        Args:
            date_ranges (list): List of tuples in the format (start_date, end_date).

        Returns:
            list: List of merged date ranges.
        """
        if not date_ranges:
            return []

        # Sort the date ranges by start date
        date_ranges.sort(key=lambda x: x[0])

        merged_ranges = []
        current_start, current_end = date_ranges[0]

        for start_date, end_date in date_ranges[1:]:
            # Check if the current range overlaps or is continuous with the next range
            if start_date <= (current_end + timedelta(days=1)):
                # Merge the ranges by updating the current_end
                current_end = max(current_end, end_date)
            else:
                # No overlap or continuity, add the current range to merged_ranges
                merged_ranges.append((current_start, current_end))
                current_start, current_end = start_date, end_date

        # Add the last range
        merged_ranges.append((current_start, current_end))

        return merged_ranges

    #------------------------------------------------Prompts------------------------------------------------

    def validate_property_names(self, doc, property_names, num_attempts = 3):
        """
        Validate the property names in the document.

        Args:
            doc (str): The document to check.
            property_names (list): List of property names.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            list: List of validated property names.
        """
        prompt = f"""You are checking the work of a junior data entry worker for a tourism company. The worker was given a document section that should contain accommodation information, and was asked to list each property available.
        A valid property should contain many sub-accommodations or accommodation types, with price and rate information for each. 
        Check the following list of properties against the provided document section for anything that should not be included - for example, considering a sub-accommodation of a certain property as a separate property.
        Be wary of sections that are introductory in nature. For instance, a large resort, "Fancyland" may have a section called "Fancyland accommodation" describing the types of accommodation, but without specific prices. It is implied that further subsections then describe each area of the resort, such as each hotel within it, with more detailed information such as breakdowns of prices. 
        In this case, you should not include this "Fancyland Accommodation" as a property name, nor the types of accommodation referenced but not fully described within it.
        Other sections may show additional fees for a property. For instance, green fees to play golf at "Hotel X". If no accommodation information on "Hotel X" is provided in this document section, do not record it as a valid property.
        Also consider whether the specified property name is the most accurate and descriptive name for the property, and if any properties are missing. For instance, if the section is titled "Rest Camps" and there is a "Rest Camp" that is not mentioned in the list, it should be considered.
        Another common mistake that the juniors make is to consider a room type to be a property. If the venue falls under the scope of a property that has already been identified, it should not be considered a separate property.
        After writing out your reasoning process, return a YAML response with the following data:
        CORRECT_PROPERTIES: a list of the valid properties. If no valid properties are found - for instance, if you determine this section is introductory in nature - return an empty list here."""

       

        body = f"{doc}\n\n PROPERTIES IDENTIFIED BY THE WORKER:"
        for property in property_names: 
            body += f"\n{property}"

        return self.llm_handler.sendMessageToLLM_Expecting_Yaml_Response(body,prompt,num_attempts=num_attempts,field_to_extract="CORRECT_PROPERTIES")


    

    def get_period_Length(self, doc,property_name, period, validity_period, num_attempts=3):
        """
        Get the date ranges for a specific period at a property.

        Args:
            doc (str): The document to check.
            property_name (str): The property name.
            period (str): The period name.
            validity_period (list): List containing the start date and end date of the overarching period.
            num_attempts (int, optional): Number of attempts to validate. Defaults to 3.

        Returns:
            list: List of date ranges for the period at the property.
        """
        prompt = f"""In the following document which should contain accommodation information, when does the {period} period start and end at "{property_name}"? Bear in mind that the period may consist of multiple date ranges.
        After stating what information is present regarding the {period} period in the document in general, and then discussing whether there is any specific statements regarding {period} for {property_name}, return a YAML response with the following data:
        Dates: A list containing only the date ranges for {period} at {property_name}. Each date range should be an array of format [start_date, end_date]. Format dates as follows: YYYYY-MM-DD.  If you cannot find a date, use a default date of 0001-01-01"""
        if len(validity_period) > 1: 
            prompt+= f"""\nAll dates should be contained within the range: {validity_period[0]} - {validity_period[1]}.\n You may use the is_within_range function to verify that your answers are within this range, if needed."""
        
        gptTools = [{
        "type": "function",
        "function": {
            "name": "is_within_range",
            "description": """Identifies any dates that are not within the valid period. You do not need to specify the validity period.""",
            "parameters": {
                "type": "object",
                "properties": {
                    "dates": {
                        "type": "array",
                        "description": "A list of dates to validate",
                        "items": {
                            "type": "string",
                            "description": "A date in YYYY-MM-DD format."
                        }
                    }
                },
                "required": ["dates"],
                "additionalProperties": False
            }
            }
        }]

        geminiTools = [is_within_range]
        attempt = 0
        while attempt < num_attempts:
            final_response = False
            if len(validity_period) > 1 and gptTools and geminiTools:
                response = self.llm_handler.sendMessageToLLM(doc,prompt,GptTools=gptTools, GeminiTools=geminiTools)
                if (len(response) == 2):
                    msg, messages = response
                else:
                    final_response = True
            else:
                response = self.llm_handler.sendMessageToLLM(doc,prompt)
                final_response = True

            while not final_response:
                if msg.tool_calls == None or len(msg.tool_calls)==0:
                    response = msg.content
                    final_response = True
                    break
                messages.append({
                            "role": "assistant",
                            "content": str(msg.content),
                            "tool_calls": msg.tool_calls
                        })
                for tool_call in msg.tool_calls:
                    name = tool_call.function.name
                    args = json.loads(tool_call.function.arguments)
                    print("Using tool:",name, args)

                    if name == "is_within_range":
                        dates = args["dates"]
                        #print(seasons)
                        result = is_within_range(validity_period[0],validity_period[1],dates)
                        
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": str(result)
                        })
                    else:
                        print("Incorrect tool use:",name)
            
                response = self.llm_handler.sendMessagesToLLM(messages, prompt, GptTools=gptTools, GeminiTools=geminiTools)
                if (len(response) == 2):
                    msg, messages = response
                else:
                    final_response = True
        
            try:
                parsed_response = self.llm_handler.parse_yaml(response)
                return parsed_response["Dates"]
            except:
                attempt += 1
            
        return {"error": "Failed to parse json response correcting element after multiple attempts."} 
    
    
    #------------------------------------------------Processing------------------------------------------------

    def processSections(self, sections, Sections_To_Do = 1000, Properties_To_Do_Per_Section = 1000):
        """
        Process multiple sections of a document.

        Args:
            sections (list): List of sections to process.
            Sections_To_Do (int, optional): Number of sections to process. Defaults to 1000.
            Properties_To_Do_Per_Section (int, optional): Number of properties to process per section. Defaults to 1000.

        Returns:
            tuple: A tuple containing the processed data and extended data.
        """
        

        

        data = []
        extended_data = []
        Sections_Done = 0
        for section in sections:
            self.general_document_parser = general_document_parser(section,self.model)
            if self.general_document_parser.get_valid():
                print("processing section...")
                local_data, local_extended_data = self.process_section(section, Properties_To_Do_Per_Section)
                if local_data and local_extended_data:
                    data.extend(local_data)
                    extended_data.extend(local_extended_data)
                    Sections_Done+=1
                    if Sections_Done>=Sections_To_Do: break
            else:
                print("invalid section")
        return data, extended_data

    from datetime import datetime, timedelta

    def process_section(self, section, Properties_To_Do = 1000):
        """
        Process a single section of a document.

        Args:
            section (str): The section to process.
            Properties_To_Do (int, optional): Number of properties to process. Defaults to 1000.

        Returns:
            tuple: A tuple containing the processed data and extended data.
        """
        self.general_document_parser = general_document_parser(section,self.model)
        data = []
        extended_data = []
        property_names = self.general_document_parser.get_property_names()
        if property_names == None or len(property_names)==0 or (len(property_names)==1 and property_names[0].strip().upper()=="NONE"):
            print("No properties detected in section")
            return None, None
        print("Properties identified:", property_names)
        validated_property_names = self.validate_property_names(section,property_names)
        print("Properties validated:", validated_property_names)
        if validated_property_names==None or len(validated_property_names)==0 or (len(validated_property_names)==1 and validated_property_names[0].strip().upper()=="NONE"):
            print("No valid detected in section")
            return None, None
        Properties_Done = 0
        validity_period = self.general_document_parser.get_overarching_period()
        overall_periods = self.general_document_parser.get_periods()
        section_has_sto = self.general_document_parser.general_has_sto()
        for property_name in validated_property_names:
            self.property_parser = property_parser(section, property_name, self.model)
            print("property name:",property_name)
            if not self.property_parser.validate_property():
                print("Property invalid! Skipping to next")
                continue

            #headings = self.get_headings(section, property_name)
                
            meal_types = self.property_parser.get_meal_types()
            print("     Property meals:", meal_types)
            periods = self.property_parser.get_periods_at_property(overall_periods)
            print("     Initial identified periods:", periods)
            
            periodsWithTimes = []
            for period in periods:
                times = self.get_period_Length(section,property_name,period, validity_period)
                #deal with possibility of periods with multiple provided date ranges
                merged_date_ranges = self.merge_date_ranges(times)
                for time in merged_date_ranges:
                    periodWithTime = (period,time[0],time[1])
                    if not periodWithTime in periodsWithTimes: #avoid duplicates
                        periodsWithTimes.append(periodWithTime)

            checked_periods = self.property_parser.check_periods(periodsWithTimes, validity_period)
            print("     Property periods:", checked_periods)

            room_types = self.property_parser.get_room_types()
            print("     Property room types:", room_types)
            for room_type in room_types:
                self.Room_Parser = room_type_parser(section, property_name, room_type,self.model)
                print(f"Processing room_type {room_type} at property {property_name}")
                meal_type = self.Room_Parser.get_meal_type(meal_types)
                periods_processed = {}
                for period in checked_periods:
                    has_sto = self.property_parser.has_sto(period[0],period[1],period[2]) if section_has_sto else False #check sto if it exists in doc
                    if has_sto:
                        data_item, extended_info = self.processPeriod(periods_processed, meal_type, period,sto_val=1) #sto
                        data.append(data_item)
                        extended_data.append(data_item + extended_info)
                        data_item, extended_info = self.processPeriod(periods_processed, meal_type, period,sto_val=0) #published
                    else:
                        data_item, extended_info = self.processPeriod(periods_processed, meal_type, period)
                   
                    data.append(data_item)
                    extended_data.append(data_item + extended_info)
            Properties_Done+=1
            if Properties_Done>=Properties_To_Do: break
        return data, extended_data
    

    def processPeriod(self, periods_processed, meal_type, period,sto_val=-1):
        """
        Process a specific period for a room type at a property.

        Args:
            periods_processed (dict): Dictionary of processed periods.
            meal_type (str): The meal type.
            period (tuple): The period name and date range.
            sto_val (int, optional): The STO value. Defaults to -1.

        Returns:
            tuple: A tuple containing the data item and extended information.
        """
        if len(period) != 3:
            print("Error - period not in expected format:", period)
            return None, None
        #because periods might exist multiple times, just do the llm calls once
        #key includes - sto potentially
        period_name_key = period[0]
        period_name_key += " - STO" if sto_val==1 else " - Published" if sto_val==0 else ""
        if period_name_key in periods_processed:
            period_info = periods_processed[period_name_key]
        else:
            

            single_room_info = self.Room_Parser.get_single_room_rate(period,sto_val)
            if 'TOTAL_CAPACITY' in single_room_info:
                total_capacity = single_room_info['TOTAL_CAPACITY']
            else:
                total_capacity = 0 
                print(f"Warning: 'TOTAL_CAPACITY' not found in single_room_info: {single_room_info}")

            period_info = {
                        "single_rate": self.sum_room_rates(single_room_info),
                        "double_rate": 0,
                        "triple_rate": 0,
                        "quad_rate": 0,
                        "child_rate": self.Room_Parser.get_child_rate(period,sto_val),
                        "child_2_rate": self.Room_Parser.get_child_2_rate(period,sto_val), 
                        "infant_rate": self.Room_Parser.get_infant_rate(period,sto_val),
                        "base_rate": single_room_info["BASE_RATE"],
                        "base_capacity": single_room_info["BASE_CAPACITY"],
                        "total_capacity": single_room_info["TOTAL_CAPACITY"],
                        "single_extra": sum([self.fixCurrency(str(fee)) for fee in single_room_info["ADDITIONAL_FEES"]]),
                        "double_extra": 0,
                        "triple_extra": 0,
                        "quad_extra": 0,
                    }
            #only call these functions if needed, to optimise
            if total_capacity >= 2:
                double_room_info = self.Room_Parser.get_double_room_rate(period,sto_val)
                period_info["double_rate"] = self.sum_room_rates(double_room_info)
                period_info["double_extra"] = sum([self.fixCurrency(str(fee)) for fee in double_room_info["ADDITIONAL_FEES"]])

                if total_capacity >= 3:
                    triple_room_info = self.Room_Parser.get_triple_room_rate( period,sto_val)
                    period_info["triple_rate"] = self.sum_room_rates(triple_room_info)
                    period_info["triple_extra"] = sum([self.fixCurrency(str(fee)) for fee in triple_room_info["ADDITIONAL_FEES"]])

                    if total_capacity >= 4:
                        quad_room_info = self.Room_Parser.get_quad_room_rate(period,sto_val)
                        period_info["quad_rate"] = self.sum_room_rates(quad_room_info)
                        period_info["quad_extra"] = sum([self.fixCurrency(str(fee)) for fee in quad_room_info["ADDITIONAL_FEES"]])

            periods_processed[period_name_key] = period_info
                    
        data_item = [self.Room_Parser.property_name, self.Room_Parser.room_type,meal_type, period[1], period[2], 
                            period_info["double_rate"], period_info["single_rate"], period_info["triple_rate"], period_info["quad_rate"],
                            period_info["child_rate"],period_info["child_2_rate"],period_info["infant_rate"]]
                
        extended_info = [period_name_key,period_info["base_rate"],period_info["base_capacity"],period_info["total_capacity"],period_info["single_extra"],period_info["double_extra"],period_info["triple_extra"],period_info["quad_extra"]]
        return data_item, extended_info
    

    def writeData(self, data, extended_data, name):
        """
        Write the processed data to CSV files.

        Args:
            data (list): The processed data.
            extended_data (list): The extended data.
            name (str): The name of the output files.
        """
        with open(f"{name}.csv", mode='w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
            writer.writerow(["Property","room type","Meal basis","DATE_FROM","DATE_TO", "Double room rate", "Single room rate","Triple room rate","Quad room rate","Child rate 1", "Child rate 2","Infant rate"])
            writer.writerows(data)

        with open(f"{name}_extended.csv", mode='w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
                    writer.writerow(["Property","room type","Meal basis","DATE_FROM","DATE_TO", "Double room rate", "Single room rate","Triple room rate","Quad room rate","Child rate 1", "Child rate 2","Infant rate",
                                    "Period","base_rate","base_capacity","total_capacity","single_extra","double_extra","tripe_extra","quad_extra"])
                    writer.writerows(extended_data)

        print(f"Data written to {name}.csv and {name}_extended.csv")

    def processExcelSheet(self, filename, sheet, outname, Properties_To_Do = 1000):
        """
        Process an Excel sheet and extract accommodation information.

        Args:
            filename (str): The path to the Excel file.
            sheet (str): The name of the sheet to process.
            outname (str): The name of the output files.
            Properties_To_Do (int, optional): Number of properties to process. Defaults to 1000.
        """
        excelstr = self.document_processor.read_excel_to_string(filename,sheet)
        if excelstr is None or len(excelstr)==0:
            print("Error on excel parsing - no document contents found")
            return
        data, extended_data = self.process_section(excelstr,Properties_To_Do)
        self.writeData(data,extended_data,outname)

    def processExcel(self, filename, outname, Properties_To_Do = 1000):
        """
        Process an Excel file and extract accommodation information.

        Args:
            filename (str): The path to the Excel file.
            outname (str): The name of the output files.
            Properties_To_Do (int, optional): Number of properties to process. Defaults to 1000.
        """
        wb = load_workbook(filename, data_only=True)
        for sheet in wb.sheetnames:
            self.processExcelSheet(filename, sheet, outname, Properties_To_Do)    

    def processPdf(self, filename, outname, Sections_To_Do = 1000, Properties_To_Do_Per_Section = 1000, force_ocr=False):
        """
        Process a PDF and extract accommodation information.

        Args:
            filename (str): The path to the PDF file.
            outname (str): The name of the output files.
            Sections_To_Do (int, optional): Number of sections to process. Defaults to 1000.
            Properties_To_Do_Per_Section (int, optional): Number of properties to process per section. Defaults to 1000.
            force_ocr (bool, optional): Whether to force OCR. Defaults to False.
        """
        length_threshold = 7000 #if doc is fewer chars than this, treat as single section
        pdfstr = self.document_processor.read_pdf_to_string(filename)

        headings, sections = self.document_processor.chunk_doc_if_needed(pdfstr, self.llm_handler, length_threshold)
        print("Processing sections:", headings)
        data, extended_data = self.processSections(sections, Sections_To_Do, Properties_To_Do_Per_Section)
        self.writeData(data,extended_data,outname)

    def processTxt(self, filename, outname, Properties_To_Do = 100):
        """
        Process a text file and extract accommodation information.

        Args:
            filename (str): The path to the text file.
            outname (str): The name of the output files.
            Properties_To_Do (int, optional): Number of properties to process. Defaults to 100.

        Returns:
            None
        """
        with open(filename,"r") as f:
            contents = f.read()
        if contents is None or len(contents)==0:
            print("Error on file parsing - no document contents found")
            return
        data, extended_data = self.process_section(contents,Properties_To_Do)
        self.writeData(data,extended_data,outname)