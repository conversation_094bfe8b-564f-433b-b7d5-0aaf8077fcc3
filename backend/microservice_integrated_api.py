import asyncio
from Doc_Processor_Microservice import doc_processor_microservice
from helpers.friends import MicroService

from flask import Flask, request, jsonify
from flask_cors import CORS

import logging
import os

#---- microservice ----
class microClient():
    def __init__(self, service_to_call="doc_processor_service"):
        self.client_service = MicroService("client_service")
        self.service_to_call = service_to_call
        self.logger = logging.getLogger(f"clientof.{service_to_call}")
        self.client_service_task = None

    async def start(self):
        self.client_service_task = asyncio.create_task(self.client_service.start(), name="client_service_task")
        await asyncio.sleep(1) # Give services time to start up

        self.logger.info("client started")
    
    async def call_service(self, func_name, args):
        self.logger.info(f"calling service {self.service_to_call}: {func_name}({args})")
        result = await self.client_service.call_service(
            self.service_to_call, 
            func_name, 
            args, 
            wait_for_response=True
        )
        return result

    async def stop(self):
        await self.client_service.stop()
        if self.client_service_task:
            await self.client_service_task 
        

client = microClient()        
#---- api ----

DEFAULT_MODEL = "gemini-2.0-flash"

class ValidationHelper:
    @staticmethod
    def validate_file_exists(filename):
        cwd = os.getcwd()
        file_path = f"temp/{filename}" if cwd.endswith("backend") else f"backend/temp/{filename}"

        if not os.path.exists(file_path):
            return False, jsonify({'error': 'File not found'}), 404
        return True, file_path, None

    @staticmethod
    def validate_required_params(data, required_params):
        missing_params = [param for param in required_params if param not in data]
        if missing_params:
            return False, jsonify({'error': f'Missing required parameters: {", ".join(missing_params)}'}), 400
        return True, None, None

    @staticmethod
    def validate_file_upload(file, allowed_extensions):
        if 'file' not in request.files:
            return False, jsonify({'error': 'No file provided'}), 400
        
        if file.filename == '':
            return False, jsonify({'error': 'No file selected'}), 400
            
        if not any(file.filename.endswith(ext) for ext in allowed_extensions):
            return False, jsonify({'error': f'File must be one of: {", ".join(allowed_extensions)}'}), 400
            
        return True, None, None

app = Flask(__name__)
# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
app.logger.setLevel(logging.DEBUG)

# Configure CORS
CORS(app, 
     resources={r"/*": {
         "origins": ["http://localhost:3000", "http://localhost:5173", "http://localhost:80", "http://127.0.0.1:3000", "http://127.0.0.1:5173", "http://127.0.0.1:80"],
         "methods": ["GET", "POST", "OPTIONS"],
         "allow_headers": ["Content-Type", "Authorization", "Accept"],
         "supports_credentials": True,
         "expose_headers": ["Content-Type", "Authorization"],
         "max_age": 600
     }},
     supports_credentials=True
)

# Add CORS headers to all responses
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', request.headers.get('Origin', '*'))
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    response.headers.add('Access-Control-Allow-Credentials', 'true')
    return response

#---- api endpoints ----


@app.route('/get-valid', methods=['POST'])
async def get_valid():
    try:
        app.logger.info(f"validating filename...") 
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return jsonify({'error': str(error_response)}), status_code 
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return jsonify({'error': str(error_response)}), 404
        
        app.logger.info(f"Processing document: {data['filename']}")    
        # try:
        #     model = data.get('model', DEFAULT_MODEL)
        #     parser = process_document_general(data['filename'], model=model)
        #     return jsonify({'valid': parser.get_valid()})
        # except Exception as e:
        #     app.logger.error(f"Error in get_valid: {str(e)}")
        #     return jsonify({'error': f'Processing error: {str(e)}'}), 500

        result = await client.call_service("get_valid",{"filename": data['filename']})
        if 'valid' in result:
            return jsonify({'valid': result['valid']})
        elif 'error' in result:
            return jsonify({'error': result['error']}), 400
        else:
            return jsonify({'error': 'Unexpected response from service'}), 500
    
    except Exception as e:
        app.logger.error(f"Unexpected error in get_valid: {str(e)}")
        return jsonify({'error': str(e)}), 500

#---- main ----

async def main():
    app.logger.info("starting...")
    await client.start()
    app.logger.info("client started")
    try:
        app.run(debug=True, host='0.0.0.0', port=5051)

        # Synchronous call (wait for response)
        # result = await client.call_service("get_valid",{"filename": "Addo_Elephant_1Page.pdf"})
        # print(result)
    finally:
        # Stop the services
        await client.stop()
        #await doc_processor_service.stop()

if __name__ == "__main__":
    asyncio.run(main())

#test with            curl -X POST http://127.0.0.1:5051/get-valid -H "Content-Type: application/json"  -d "{\"filename\": \"Addo_Elephant_1Page.pdf\"}"