#!/bin/bash

# Startup script for Tourvest Document Processor Backend

# Set default environment if not provided
export FLASK_ENV=${FLASK_ENV:-production}
export FLASK_APP=${FLASK_APP:-ProcessorAPI.py}
export WORKERS=${WORKERS:-4}

echo "Starting Tourvest Document Processor Backend..."
echo "Environment: $FLASK_ENV"
echo "Workers: $WORKERS"

# Create necessary directories (permissions handled by Dockerfile)
mkdir -p temp temp_csvs

if [ "$FLASK_ENV" = "production" ]; then
    echo "Starting with Gunicorn (Production mode)"
    exec gunicorn --config gunicorn.conf.py ProcessorAPI:app
else
    echo "Starting with Flask dev server (Development mode)"
    export FLASK_DEBUG=1
    exec python ProcessorAPI.py
fi
