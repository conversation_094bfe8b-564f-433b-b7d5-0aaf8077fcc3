import csv
import uuid
import new_output
from helpers.LLM_Handler import LLM_Handler
from helpers.model_utils import upgrade_model_to_level_2
from general_document_parser import general_document_parser
from property_parser import property_parser
from flask import Flask, abort, request, jsonify, send_file, g
from flask_cors import CORS
from prometheus_flask_exporter import PrometheusMetrics
from TourvestServiceDocProcessor_combined import document_processor
import os
import logging
from helpers.Document_Processing import Doc_Processor
from room_type_parser import room_type_parser
from apscheduler.schedulers.background import BackgroundScheduler
import atexit
import time
import threading


# Directory to store temporary files
TEMP_CSV_DIR = 'temp_csvs'
TEMP_FILE_PROCESSING_DIR = 'temp'

DEFAULT_GEMINI_MODEL = "gemini-2.0-flash"
DEFAULT_GEMINI_MODEL_LEVEL_2 = LLM_Handler.latest_medium_gemini_model()

DEFAULT_GPT_MODEL = "gpt-4o-mini"
DEFAULT_GPT_MODEL_LEVEL_2 = LLM_Handler.latest_medium_openai_model()



DOWNLOAD_CLEANUP_DELAY_SECONDS = 10 * 60  # 10 minutes after download
INACTIVITY_CLEANUP_THRESHOLD_HOURS = 24   # Delete files older than 24 hours
CLEANUP_JOB_INTERVAL_HOURS = 6            # Run cleanup job every 6 hours

# Global dictionary to store task statuses
task_statuses = {}

class ValidationHelper:
    @staticmethod
    def validate_file_exists(filename):
        file_path = f"{TEMP_FILE_PROCESSING_DIR}/{filename}"
        if not os.path.exists(file_path):
            return False, jsonify({'error': 'File not found'}), 404
        return True, file_path, None

    @staticmethod
    def validate_required_params(data, required_params):
        missing_params = [param for param in required_params if param not in data]
        if missing_params:
            return False, jsonify({'error': f'Missing required parameters: {", ".join(missing_params)}'}), 400
        return True, None, None

    @staticmethod
    def validate_file_upload(file, allowed_extensions):
        if 'file' not in request.files:
            return False, jsonify({'error': 'No file provided'}), 400
        
        if file.filename == '':
            return False, jsonify({'error': 'No file selected'}), 400
            
        if not any(file.filename.endswith(ext) for ext in allowed_extensions):
            return False, jsonify({'error': f'File must be one of: {", ".join(allowed_extensions)}'}), 400
            
        return True, None, None

app = Flask(__name__)
metrics = PrometheusMetrics(app)

# Define basic counters using prometheus_client directly
# Import prometheus_client after prometheus-flask-exporter to avoid conflicts
import prometheus_client

# Create custom metrics using prometheus_client
processed_documents_total = prometheus_client.Counter(
    'processed_documents_total',
    'Total documents processed',
    ['doc_type', 'status'],
    registry=metrics.registry
)
app.logger.info("Registered metric: processed_documents_total")

file_uploads_total = prometheus_client.Counter(
    'file_uploads_total',
    'Total files uploaded',
    ['file_type'],
    registry=metrics.registry
)
app.logger.info("Registered metric: file_uploads_total")

auto_process_tasks_total = prometheus_client.Counter(
    'auto_process_tasks_total',
    'Total auto-processing tasks started',
    registry=metrics.registry
)
app.logger.info("Registered metric: auto_process_tasks_total")

document_processing_errors_total = prometheus_client.Counter(
    'document_processing_errors_total',
    'Total errors during document processing',
    ['error_type'],
    registry=metrics.registry
)
app.logger.info("Registered metric: document_processing_errors_total")

# New metrics
api_request_duration = prometheus_client.Histogram(
    'api_request_duration_seconds',
    'Duration of API requests in seconds',
    ['endpoint', 'method'],
    registry=metrics.registry
)
app.logger.info("Registered metric: api_request_duration_seconds")

api_requests_total = prometheus_client.Counter(
    'api_requests_total',
    'Total number of API requests',
    ['endpoint', 'method', 'status'],
    registry=metrics.registry
)
app.logger.info("Registered metric: api_requests_total")

document_processing_duration = prometheus_client.Histogram(
    'document_processing_duration_seconds',
    'Duration of document processing operations in seconds',
    ['operation_type'],
    registry=metrics.registry
)
app.logger.info("Registered metric: document_processing_duration_seconds")

csv_operations_total = prometheus_client.Counter(
    'csv_operations_total',
    'Total number of CSV operations',
    ['operation_type'],
    registry=metrics.registry
)
app.logger.info("Registered metric: csv_operations_total")

active_tasks = prometheus_client.Gauge(
    'active_processing_tasks',
    'Number of currently active processing tasks',
    registry=metrics.registry
)
app.logger.info("Registered metric: active_processing_tasks")

# Add a test metric to verify basic functionality
test_metric = prometheus_client.Counter(
    'test_metric_total',
    'A test metric to verify Prometheus scraping',
    registry=metrics.registry
)
app.logger.info("Registered metric: test_metric_total")

# Add a test increment on startup
test_metric.inc()
app.logger.info("Incremented test_metric_total")

# Configure logging based on environment
FLASK_ENV = os.getenv('FLASK_ENV', 'development')
if FLASK_ENV == 'production':
    # Production logging configuration
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(name)s %(message)s'
    )
    app.logger.setLevel(logging.INFO)
else:
    # Development logging configuration (commented for production)
    logging.basicConfig(level=logging.DEBUG)
    app.logger.setLevel(logging.DEBUG)

# Configure CORS based on environment
if FLASK_ENV == 'production':
    # Production CORS configuration
    CORS(app,
         resources={r"/*": {
             "origins": ["https://tourvest.darcartz.com", "https://tdm_contract_processor.sapconet.org"],
             "methods": ["GET", "POST", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "Accept"],
             "supports_credentials": True,
             "expose_headers": ["Content-Type", "Authorization", "Content-Disposition"],
             "max_age": 3600  # Longer cache for production
         }},
         supports_credentials=True
    )
else:
    # Development CORS configuration (commented for production)
    CORS(app,
         resources={r"/*": {
             "origins": ["http://localhost:400", "http://localhost:3000", "http://localhost:5173", "http://localhost:80", "http://127.0.0.1:3000", "http://127.0.0.1:5173", "http://127.0.0.1:80", "https://tourvest.darcartz.com"],
             "methods": ["GET", "POST", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "Accept"],
             "supports_credentials": True,
             "expose_headers": ["Content-Type", "Authorization", "Content-Disposition"],
             "max_age": 600
         }},
         supports_credentials=True
    )

# Add CORS headers to all responses
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', request.headers.get('Origin', '*'))
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    response.headers.add('Access-Control-Allow-Credentials', 'true')
    response.headers.add('Access-Control-Expose-Headers', 'Content-Type,Authorization,Content-Disposition')
    return response

processor = document_processor()

# Production error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    if FLASK_ENV == 'production':
        # Don't expose internal errors in production
        return jsonify({'error': 'Internal server error'}), 500
    else:
        # Show detailed errors in development
        return jsonify({'error': str(error)}), 500

@app.errorhandler(Exception)
def handle_exception(e):
    app.logger.error(f"Unhandled exception: {str(e)}")
    if FLASK_ENV == 'production':
        return jsonify({'error': 'Internal server error'}), 500
    else:
        return jsonify({'error': str(e)}), 500

@app.route('/test', methods=['GET'])
def test_route():
    return "Hello, World!"

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for production monitoring"""
    try:
        # Basic health checks
        status = {
            'status': 'healthy',
            'environment': FLASK_ENV,
            'timestamp': time.time(),
            'temp_dirs_exist': {
                'csv_dir': os.path.exists(TEMP_CSV_DIR),
                'processing_dir': os.path.exists(TEMP_FILE_PROCESSING_DIR)
            }
        }
        return jsonify(status), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/upload-pdf', methods=['POST'])
def upload_pdf():
    start_time = time.time()
    try:
        file_uploads_total.labels(file_type='pdf').inc()
        file = request.files['file']
        is_valid, error_response, status_code = ValidationHelper.validate_file_upload(file, ['.pdf'])
        if not is_valid:
            api_requests_total.labels(endpoint='/upload-pdf', method='POST', status=status_code).inc()
            return error_response, status_code
            
        # Save the uploaded file temporarily
        os.makedirs(TEMP_FILE_PROCESSING_DIR, exist_ok=True)
        file_path = f"{TEMP_FILE_PROCESSING_DIR}/{file.filename}"
        file.save(file_path)
        app.logger.info(f"PDF file {file.filename} uploaded successfully.")
        
        # Get document chunks
        try:
            with document_processing_duration.labels(operation_type='chunking').time():
                response = return_Document_Chunks(file.filename)
            chunks_data = response.get_json()
            headings = chunks_data['headings']
            sections = chunks_data['sections']
            
            # Save each chunk as a separate file
            chunk_files = []
            base_name = os.path.splitext(file.filename)[0]
            
            # Delete existing chunk files if they exist, to avoid conflicts
            for existing_file in os.listdir(TEMP_FILE_PROCESSING_DIR):
                if existing_file.startswith(base_name) and existing_file.endswith('.txt'):
                    existing_file_path = os.path.join(TEMP_FILE_PROCESSING_DIR, existing_file)
                    delete_file_safely(existing_file_path)

            for heading, section in zip(headings, sections):
                # Create a safe filename from the heading
                safe_heading = "".join(c for c in heading if c.isalnum() or c in (' ', '-', '_')).rstrip()
                chunk_filename = f"{base_name}_{safe_heading}.txt"
                chunk_path = f"{TEMP_FILE_PROCESSING_DIR}/{chunk_filename}"
                
                # Save the chunk
                if not os.path.exists(chunk_path):
                    with open(chunk_path, 'w', encoding='utf-8') as f:
                        f.write(section)
                chunk_files.append(chunk_filename)
            
            api_requests_total.labels(endpoint='/upload-pdf', method='POST', status=200).inc()
            return jsonify({
                'message': 'File uploaded and chunked successfully',
                'original_filename': file.filename,
                'chunk_files': chunk_files
            })
            
        except Exception as e:
            app.logger.error(f"Error during chunking for {file.filename}: {str(e)}")
            document_processing_errors_total.labels(error_type='chunking_error').inc()
            api_requests_total.labels(endpoint='/upload-pdf', method='POST', status=500).inc()
            return jsonify({'error': f'Error during document chunking: {str(e)}'}), 500
            
    except Exception as e:
        document_processing_errors_total.labels(error_type='upload_error').inc()
        api_requests_total.labels(endpoint='/upload-pdf', method='POST', status=500).inc()
        return jsonify({'error': str(e)}), 500
    finally:
        duration = time.time() - start_time
        api_request_duration.labels(endpoint='/upload-pdf', method='POST').observe(duration)



#------------------Document Parsing Helper Functions------------------

# Helper function to process the document
def get_doc_string(filename):
    try:
        file_path = f"{TEMP_FILE_PROCESSING_DIR}/{filename}"
        if filename.endswith('.txt'):  # For chunk files
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:  # For PDF files
            return Doc_Processor().read_pdf_to_string(file_path)
    except Exception as e:
        app.logger.error(f"Error parsing document as string: {str(e)}")
        raise e

def process_document_general(filename, model=DEFAULT_GEMINI_MODEL):
    try:
        pdfstr = get_doc_string(filename)
        return general_document_parser(pdfstr, model=model)
    except Exception as e:
        app.logger.error(f"Error processing general document: {str(e)}")
        raise e
    
def process_document_property(filename, property_name, model=DEFAULT_GEMINI_MODEL):
    try:
        pdfstr = get_doc_string(filename)
        return property_parser(pdfstr, property_name, model=model)
    except Exception as e:
        app.logger.error(f"Error processing property document: {str(e)}")
        raise e

def process_document_room_type(filename, property_name, room_type, model=DEFAULT_GEMINI_MODEL):
    try:
        pdfstr = get_doc_string(filename)
        return room_type_parser(pdfstr, property_name, room_type, model=model)
    except Exception as e:
        app.logger.error(f"Error processing property document: {str(e)}")
        raise e

def return_Document_Chunks(filename, model=DEFAULT_GEMINI_MODEL_LEVEL_2, token_threshold_margin=0.9):
    """
    Determines whether to chunk a document based on token count.

    Args:
        filename (str): The path to the PDF file.
        model (str): The identifier for the LLM to be used.
        token_threshold_margin (float): A margin to apply to the model's max token limit
                                       (e.g., 0.9 for 90%) to leave space for prompts/overhead.
    """
    try:
        # Upgrade model to level 2 if level 1 model is provided
        model = upgrade_model_to_level_2(model, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)

        pdfstr = get_doc_string(filename)
        llm_handler = LLM_Handler(model)

        # 1. Get the model's maximum token limit

        model_max_tokens = llm_handler.get_max_input_tokens()
        if model_max_tokens==None:
            # Fallback if LLM_Handler doesn't have this method yet
            # You might have a predefined dictionary or a more robust way to get this
            app.logger.warning(f"LLM_Handler does not know max tokens for model {model}")
            model_max_tokens = 128000 #based on 4o mini
            app.logger.info(f"Using assumed max tokens: {model_max_tokens} for model {model}")


        # 2. Define the actual length threshold in tokens for chunking
        # This could be slightly less than the model's max tokens to leave room for instructions, etc.
        effective_token_threshold = int(model_max_tokens * token_threshold_margin)

        # 3. Count tokens in the document
        num_tokens = llm_handler.get_token_count(pdfstr)

        app.logger.info(f"Document token count: {num_tokens}. Model: {model}. Effective token threshold for chunking: {effective_token_threshold}.")

        # 4. Call chunk_doc_if_needed with the token-based threshold
        # The Doc_Processor().chunk_doc_if_needed will need to be updated
        # to understand and use this token_threshold.
        headings, sections = Doc_Processor().chunk_doc_if_needed(
            pdfstr,
            llm_handler, # May need to pass the tokenizer or model info too
            length_is_tokens=True, 
            length_threshold=effective_token_threshold
        )

        # Convert dict_values to lists for JSON serialization
        app.logger.info(f"Headings: {headings}")
        if isinstance(headings, dict):
            headings = list(headings.values())
        if isinstance(sections, dict):
            sections = list(sections.values())

        return jsonify({'headings': headings, 'sections': sections, 'token_count': num_tokens, 'chunking_threshold_tokens': effective_token_threshold})

    except Exception as e:
        app.logger.error(f"Error returning document chunks: {str(e)}")
        # Consider logging the traceback for more detailed debugging
        import traceback
        app.logger.error(traceback.format_exc())
        raise e


def delete_file_safely(filepath):
    """Safely deletes a file and logs the action."""
    try:
        if os.path.exists(filepath):
            os.remove(filepath)
            app.logger.info(f"Successfully deleted temporary file: {filepath}")
        else:
            app.logger.info(f"Attempted to delete non-existent file: {filepath}")
    except Exception as e:
        app.logger.error(f"Error deleting file {filepath}: {e}")

def with_app_context(func, *args, **kwargs):
    """Helper to run a function within the Flask app context, useful for threads."""
    with app.app_context():
        return func(*args, **kwargs)

#------------------General Routes------------------

@app.route('/get-valid', methods=['POST'])
def get_valid():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        app.logger.info(f"Processing document: {data['filename']}")    
        try:
            model = data.get('model', DEFAULT_GEMINI_MODEL_LEVEL_2)
            # Upgrade model to level 2 if level 1 model is provided
            #model = upgrade_model_to_level_2(model, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)
            parser = process_document_general(data['filename'], model=model)
            return jsonify({'valid': parser.get_valid()})
        except Exception as e:
            app.logger.error(f"Error in get_valid: {str(e)}")
            return jsonify({'error': f'Processing error: {str(e)}'}), 500
    
    except Exception as e:
        app.logger.error(f"Unexpected error in get_valid: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/get-property-names', methods=['POST'])
def get_property_names():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
            
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_general(data['filename'], model=model)
        return jsonify({'property_names': parser.get_property_names()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get-overarching-period', methods=['POST'])
def get_overarching_period():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
            
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_general(data['filename'], model=model)
        period = parser.get_overarching_period()
        return jsonify({'start_date': period[0], 'end_date': period[1]})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get-periods', methods=['POST'])
def get_periods():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
            
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_general(data['filename'], model=model)
        return jsonify({'periods': parser.get_periods()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/general-has-sto', methods=['POST'])
def general_has_sto():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
            
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_general(data['filename'], model=model)
        return jsonify({'has_sto': parser.general_has_sto()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/general-has-weekly_rates', methods=['POST'])
def general_has_weekly_rates():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
            
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_general(data['filename'], model=model)
        distinct_rates = parser.general_has_distinct_week_rates()
        if not distinct_rates or not isinstance(distinct_rates, list) or len(distinct_rates)==0:
            distinct_rates = ['Normal']
        return jsonify({'distinct_weekly_rates': distinct_rates})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/get-raw-section', methods=['POST'])
def get_raw_section():
    """
    Reads and returns the contents of a .txt file in the TEMP_FILE_PROCESSING_DIR if it exists.
    Expects JSON payload with 'filename'.
    """
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            return error_response, status_code

        filename = data['filename']
        if not filename.endswith('.txt'):
            return jsonify({'error': 'Invalid file type. Only .txt files are supported.'}), 400

        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(filename)
        if not is_valid:
            return error_response

        # Read and return the file contents
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        return jsonify({'content': content})

    except Exception as e:
        app.logger.error(f"Error in get_raw_section: {str(e)}")
        return jsonify({'error': str(e)}), 500

#------------------Property Specific Routes------------------

@app.route('/property-includes', methods=['POST'])
def property_includes():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        return jsonify({'includes': parser.get_includes()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
@app.route('/property-excludes', methods=['POST'])
def property_excludes():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        return jsonify({'excludes': parser.get_excludes()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/validate-property', methods=['POST'])
def validate_property():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        return jsonify({'valid': parser.validate_property()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/property-room-types', methods=['POST'])
def property_room_types():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        return jsonify({'room_types': parser.get_room_types()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/property-meal-types', methods=['POST'])
def property_meal_types():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        return jsonify({'meal_types': parser.get_meal_types()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/property-levies', methods=['POST'])
def property_levies():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        levies = parser.get_levy()
        app.logger.info(f"levy response: {levies}" )
        if isinstance(levies, str):
            levies = None
        elif not isinstance(levies, list):
            levies = [levies]
        return jsonify({'levies': levies})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/check-periods', methods=['POST'])
def check_periods():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(
            data, ['filename', 'property_name', 'periods', 'start_date', 'end_date']
        )
        if not is_valid:
            return error_response, status_code

        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response

        model = data.get('model', DEFAULT_GEMINI_MODEL_LEVEL_2)
        # Upgrade model to level 2 if level 1 model is provided
        model = upgrade_model_to_level_2(model, DEFAULT_GEMINI_MODEL, DEFAULT_GPT_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        if parser is None:
            app.logger.error('process_document_property returned None')
            return jsonify({'error': 'Internal error: failed to parse document property.'}), 500
        if not hasattr(parser, 'check_periods') or not callable(getattr(parser, 'check_periods', None)):
            app.logger.error('parser.check_periods is not callable or missing')
            return jsonify({'error': 'Internal error: parser.check_periods is not callable or missing.'}), 500
        checked_periods = parser.check_periods(data['periods'], [data['start_date'], data['end_date']])
        return jsonify({'periods': checked_periods})

    except Exception as e:
        app.logger.error(str(e))
        return jsonify({'error': str(e)}), 500
    

@app.route('/property-child-policy', methods=['POST'])
def property_child_policy():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_property(data['filename'], data['property_name'], model=model)
        return jsonify({'child_policy': parser.general_child_policy()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

#------------------Room Type Specific Routes------------------

@app.route('/room-meal-type', methods=['POST'])
def room_meal_type():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type', 'meal_types'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'meal_type': parser.get_meal_type(data['meal_types'])})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    


@app.route('/room-capacity', methods=['POST'])
def room_capacity():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'capacity': parser.get_room_capacity()})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    

@app.route('/room-single-room-rate', methods=['POST'])
def room_single_room_rate():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        rate_type = data.get('rate_type',"Normal")
        if rate_type == "Normal":
            rate_type = None
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'single_room_rate': parser.get_single_room_rate(data['period'], rate_type=rate_type)})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/room-double-room-rate', methods=['POST'])
def room_double_room_rate():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        rate_type = data.get('rate_type',"Normal")
        if rate_type == "Normal":
            rate_type = None
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'double_room_rate': parser.get_double_room_rate(data['period'], rate_type=rate_type)})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
@app.route('/room-triple-room-rate', methods=['POST'])
def room_triple_room_rate():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        rate_type = data.get('rate_type',"Normal")
        if rate_type == "Normal":
            rate_type = None
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'triple_room_rate': parser.get_triple_room_rate(data['period'], rate_type=rate_type)})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
@app.route('/room-quad-room-rate', methods=['POST'])
def room_quad_room_rate():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        rate_type = data.get('rate_type',"Normal")
        if rate_type == "Normal":
            rate_type = None
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'quad_room_rate': parser.get_quad_room_rate(data['period'], rate_type=rate_type)})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
@app.route('/room-child-rate', methods=['POST'])
def room_child_rate():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        rate_type = data.get('rate_type',"Normal")
        if rate_type == "Normal":
            rate_type = None
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'child_rate': parser.get_child_rate(data['period'], rate_type=rate_type)})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
@app.route('/room-children-rates', methods=['POST'])
def room_children_rates():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        rate_type = data.get('rate_type',"Normal")
        if rate_type == "Normal":
            rate_type = None
        child_categories = data.get('child_categories',None)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'child_rate': parser.get_children_rates(data['period'], rate_type=rate_type, child_categories=child_categories)})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/room-infant-rate', methods=['POST'])
def room_infant_rate():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type','period'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'infant_rate': parser.get_infant_rate(data['period'])})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/room-min-night-stay', methods=['POST'])
def room_min_night_stay():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type'])
        if not is_valid:
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response
        
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        min_night_stay = parser.min_night_stay()
        try:
            min_night_stay = int(min_night_stay)
        except:
            min_night_stay = -1
        if min_night_stay ==-1:
            min_night_stay = parser.min_night_stay_complex()
        return jsonify({'min_night_stay': min_night_stay})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500
 
  


@app.route('/room-cancellation-policy', methods=['POST'])
def cancellation_policy():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name','room_type'])
        if not is_valid:
            return error_response, status_code

        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response

        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'cancellation_policy': parser.cancellation_policy()})

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
@app.route('/room-pay-stays', methods=['POST'])
def pay_stays():
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename', 'property_name', 'room_type'])
        if not is_valid:
            return error_response, status_code

        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            return error_response

        model = data.get('model', DEFAULT_GEMINI_MODEL)
        parser = process_document_room_type(data['filename'], data['property_name'], data['room_type'], model=model)
        return jsonify({'pay_stays': parser.get_stay_type()})

    except Exception as e:
        return jsonify({'error': str(e)}), 500
    
#------------------ Full document processing ------------------

def run_document_processing(task_id, filename, model):
    """The actual long-running task."""
    with app.app_context():
        try:
            task_statuses[task_id] = {'status': 'processing'}
            pdfstr = get_doc_string(filename)
            
            app.logger.info(f"Starting auto-processing for {filename} with task ID {task_id}...")
            start_time = time.time()

            with document_processing_duration.labels(operation_type='auto_process').time():
                csv_path = new_output.get_doc_data(filename, pdfstr, model=model, path=TEMP_CSV_DIR)
            
            end_time = time.time()
            processing_time = end_time - start_time
            app.logger.info(f"Finished auto-processing for {filename}. Time taken: {processing_time:.2f} seconds.")
            
            task_statuses[task_id] = {'status': 'completed', 'result': csv_path}
            processed_documents_total.labels(doc_type='auto', status='success').inc()
            app.logger.info(f"Task {task_id}: Successfully auto-processed {filename}. Output: {csv_path}")

        except Exception as e:
            document_processing_errors_total.labels(error_type='auto_process_error').inc()
            processed_documents_total.labels(doc_type='auto', status='error').inc()
            app.logger.error(f"Task {task_id}: Failed to auto-process {filename}. Error: {str(e)}")
            task_statuses[task_id] = {'status': 'failed', 'error': str(e)}
        finally:
            active_tasks.dec()

# fully process the document
@app.route('/auto-process-document', methods=['POST'])
def auto_process_document():
    start_time = time.time()
    try:
        data = request.get_json()
        is_valid, error_response, status_code = ValidationHelper.validate_required_params(data, ['filename'])
        if not is_valid:
            api_requests_total.labels(endpoint='/auto-process-document', method='POST', status=status_code).inc()
            return error_response, status_code
        
        is_valid, file_path, error_response = ValidationHelper.validate_file_exists(data['filename'])
        if not is_valid:
            api_requests_total.labels(endpoint='/auto-process-document', method='POST', status=404).inc()
            return error_response
            
        model = data.get('model', DEFAULT_GEMINI_MODEL)
        task_id = str(uuid.uuid4())
        
        # Add the job to the scheduler
        scheduler.add_job(
            run_document_processing,
            args=[task_id, data['filename'], model],
            id=task_id,
            replace_existing=True
        )
        
        task_statuses[task_id] = {'status': 'pending'}
        auto_process_tasks_total.inc()
        active_tasks.inc()
        app.logger.info(f"Task {task_id} initiated for auto-processing file {data['filename']} with model {model}.")
        
        api_requests_total.labels(endpoint='/auto-process-document', method='POST', status=202).inc()
        return jsonify({'task_id': task_id}), 202
    
    except Exception as e:
        document_processing_errors_total.labels(error_type='auto_process_error').inc()
        api_requests_total.labels(endpoint='/auto-process-document', method='POST', status=500).inc()
        app.logger.error(e)
        return jsonify({'error': str(e)}), 500
    finally:
        duration = time.time() - start_time
        api_request_duration.labels(endpoint='/auto-process-document', method='POST').observe(duration)

@app.route('/task-status/<task_id>', methods=['GET'])
def task_status(task_id):
    status_info = task_statuses.get(task_id, {'status': 'not_found'})
    return jsonify(status_info)
    
# Gradual CSV Creation

# Define the expected CSV header
CSV_HEADER_ESSENTIAL_COLS = ["Property", "Room type", "Meal basis", "DATE_FROM", "DATE_TO", "Min stay", "Includes", "Excludes","Max adults", "Max Children","Max A+C",]
CSV_HEADER = [
    "Supplier","Property", "Room type", "Meal basis", "DATE_FROM", "DATE_TO", "Min stay", "Includes", "Excludes",  "Period", 
    "Child 1 From age", "Child 1 To age", "Child 2 From age", "Child 2 To age", "Child 3 From age", "Child 3 To age", "Child 4 From age", "Child 4 To age", "Child 5 From age", "Child 5 To age",
    "Max adults", "Max Children","Max A+C",
    "Cancellation Policy from days 1", "Cancellation fee % 1", "Cancellation Policy from days 2", "Cancellation fee % 2", "Cancellation Policy from days 3", "Cancellation fee % 3", "Cancellation Policy from days 4", "Cancellation fee % 4",
    "Pay stay days 1", "Pay stay free nights 1", "Pay stay days 2", "Pay stay free nights 2", "Pay stay days 3", "Pay stay free nights 3", "Pay stay days 4", "Pay stay free nights 4",
    "Single room rate", "Double room rate", "Triple room rate", "Quad room rate", "Child rate 1", "Child rate 2", "Infant rate"
]

#{
#   "Property": "Mimosa Lodge",
#   "Room type": "Classic Room",
#   "Includes": "Accommodation",
#   "Excludes": "Telephone calls, Safari Shop purchases, gratuities, all items of a personal nature, champagne, cognacs, fine wines, premium brand spirits, cigars, landing fees, self-catering, credit card payment on STO rates, dinner during stay pay specials.",
#   "Meal basis": "B&B - Bed & Breakfast",
#   "Child 1 From age": 0,
#   "Child 1 To age": 2,
#   "Max adults": 2,
#   "Max Children": 1,
#   "Max A+C": 3,
#   "Cancellation Policy from days 1": 0,
#   "Cancellation fee % 1": 0,
#   "Pay stay days": 3,
#   "Pay stay free nights": 1,
#   "Minimum Night Stay": 0,
#   "DATE_FROM": "",
#   "DATE_TO": "",
#   "Single room Rate": 783,
#   "Single room Additional Rate": 0,
#   "Double room Rate": 692,
#   "Double room Additional Rate": ""
# }

@app.route('/initialize_csv', methods=['POST'])
def initialize_csv():
    start_time = time.time()
    try:
        file_id = str(uuid.uuid4())
        filename = f"{file_id}.csv"
        filepath = os.path.join(TEMP_CSV_DIR, filename)

        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(CSV_HEADER)

        csv_operations_total.labels(operation_type='initialize').inc()
        api_requests_total.labels(endpoint='/initialize_csv', method='POST', status=201).inc()
        return jsonify({"message": "CSV initialized successfully", "file_id": file_id}), 201

    except Exception as e:
        document_processing_errors_total.labels(error_type='csv_initialize_error').inc()
        api_requests_total.labels(endpoint='/initialize_csv', method='POST', status=500).inc()
        return jsonify({"error": "Failed to initialize CSV file", "details": str(e)}), 500
    finally:
        duration = time.time() - start_time
        api_request_duration.labels(endpoint='/initialize_csv', method='POST').observe(duration)

@app.route('/append-row', methods=['POST'])
def append_row():
    start_time = time.time()
    try:
        if not request.is_json:
            api_requests_total.labels(endpoint='/append-row', method='POST', status=400).inc()
            return jsonify({"error": "Request must be JSON"}), 400

        content = request.get_json()
        file_id = content.get('file_id')
        row_data_input = content.get('data')

        if not file_id or not row_data_input:
            api_requests_total.labels(endpoint='/append-row', method='POST', status=400).inc()
            return jsonify({"error": "Missing 'file_id' or 'data' in request body"}), 400

        filename = f"{file_id}.csv"
        filepath = os.path.join(TEMP_CSV_DIR, filename)

        if not os.path.abspath(filepath).startswith(os.path.abspath(TEMP_CSV_DIR)):
            api_requests_total.labels(endpoint='/append-row', method='POST', status=400).inc()
            return jsonify({"error": "Invalid file_id"}), 400

        if not os.path.exists(filepath):
            api_requests_total.labels(endpoint='/append-row', method='POST', status=404).inc()
            return jsonify({"error": f"CSV file not found for file_id: {file_id}"}), 404

        row_to_write = []
        if isinstance(row_data_input, dict):
            # Ensure all header columns are present, default to empty string if missing
            for header in CSV_HEADER:
                row_to_write.append(row_data_input.get(header, ''))


        elif isinstance(row_data_input, list):
            # Ensure the list has the correct number of columns
            if len(row_data_input) != len(CSV_HEADER):
                api_requests_total.labels(endpoint='/append-row', method='POST', status=400).inc()
                return jsonify({
                    "error": f"Incorrect number of columns. Expected {len(CSV_HEADER)}, got {len(row_data_input)}."
                }), 400
            row_to_write = row_data_input
        else:
            api_requests_total.labels(endpoint='/append-row', method='POST', status=400).inc()
            return jsonify({"error": "'data' must be a JSON object (dictionary) or array (list)"}), 400

        with open(filepath, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(row_to_write)

        csv_operations_total.labels(operation_type='append').inc()
        api_requests_total.labels(endpoint='/append-row', method='POST', status=200).inc()
        return jsonify({"message": "Row appended successfully"}), 200

    except Exception as e:
        document_processing_errors_total.labels(error_type='csv_append_error').inc()
        api_requests_total.labels(endpoint='/append-row', method='POST', status=500).inc()
        return jsonify({"error": "Failed to append row", "details": str(e)}), 500

def remove_empty_columns_from_csv(input_filepath, output_filepath):
    """
    Removes columns that have no values (all empty) from a CSV file,
    except for columns that are in CSV_HEADER_ESSENTIAL_COLS.

    Args:
        input_filepath (str): Path to the input CSV file
        output_filepath (str): Path to the output CSV file
    """
    try:
        with open(input_filepath, 'r', newline='', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            rows = list(reader)

        if not rows:
            # Empty file, just copy it
            with open(output_filepath, 'w', newline='', encoding='utf-8') as outfile:
                pass
            return

        header = rows[0]
        data_rows = rows[1:]

        # Determine which columns to keep
        columns_to_keep = []
        for col_index, col_name in enumerate(header):
            # Always keep essential columns
            if col_name in CSV_HEADER_ESSENTIAL_COLS:
                columns_to_keep.append(col_index)
                continue

            # Check if column has any non-empty values
            has_data = False
            for row in data_rows:
                if col_index < len(row) and row[col_index].strip():
                    has_data = True
                    break

            if has_data:
                columns_to_keep.append(col_index)

        # Write the filtered CSV
        with open(output_filepath, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)

            # Write filtered header
            filtered_header = [header[i] for i in columns_to_keep]
            writer.writerow(filtered_header)

            # Write filtered data rows
            for row in data_rows:
                filtered_row = []
                for col_index in columns_to_keep:
                    if col_index < len(row):
                        filtered_row.append(row[col_index])
                    else:
                        filtered_row.append('')
                writer.writerow(filtered_row)

        app.logger.info(f"Filtered CSV: kept {len(columns_to_keep)} out of {len(header)} columns")

    except Exception as e:
        app.logger.error(f"Error filtering CSV columns: {str(e)}")
        document_processing_errors_total.labels(error_type='csv_append_error').inc()

        # If filtering fails, copy the original file
        import shutil
        shutil.copy2(input_filepath, output_filepath)

@app.route('/download-csv/<file_id>', methods=['GET'])
def download_csv(file_id):
    """
    Allows downloading the CSV file identified by file_id.
    Removes empty columns (except essential ones) before download.
    Args:
        file_id (str): The unique identifier of the CSV file.
    Returns:
        File download response or 404 error if file not found.
    """
    start_time = time.time()
    filename = f"{file_id}.csv"
    filepath_abs = os.path.abspath(os.path.join(TEMP_CSV_DIR, filename))

    if not filepath_abs.startswith(os.path.abspath(TEMP_CSV_DIR)):
        api_requests_total.labels(endpoint='/download-csv', method='GET', status=400).inc()
        processed_documents_total.labels(doc_type='manual', status='400').inc()
        abort(400, description="Invalid file ID format.")

    if not os.path.exists(filepath_abs):
        api_requests_total.labels(endpoint='/download-csv', method='GET', status=404).inc()
        processed_documents_total.labels(doc_type='manual', status='404').inc()
        abort(404, description="CSV file not found.")

    try:
        print(f"Serving file for download: {filepath_abs}") # Server-side logging

        # Create a filtered version of the CSV
        filtered_filename = f"{file_id}_filtered.csv"
        filtered_filepath = os.path.abspath(os.path.join(TEMP_CSV_DIR, filtered_filename))

        # Remove empty columns from the CSV
        remove_empty_columns_from_csv(filepath_abs, filtered_filepath)

        # Use send_from_directory for secure file serving
        response = send_file(
            filtered_filepath,
            as_attachment=True,
            download_name=os.path.basename(filepath_abs),  # Keep original filename for download
            mimetype='text/csv'
        )

        # Schedule cleanup for both original and filtered files
        timer_original = threading.Timer(
            DOWNLOAD_CLEANUP_DELAY_SECONDS,
            lambda: with_app_context(delete_file_safely, filepath_abs)
        )
        timer_filtered = threading.Timer(
            DOWNLOAD_CLEANUP_DELAY_SECONDS,
            lambda: with_app_context(delete_file_safely, filtered_filepath)
        )
        timer_original.start()
        timer_filtered.start()
        app.logger.info(f"Scheduled {filepath_abs} and {filtered_filepath} for deletion in {DOWNLOAD_CLEANUP_DELAY_SECONDS} seconds.")

        return response

    except Exception as e:
        document_processing_errors_total.labels(error_type='csv_download_error').inc()
        api_requests_total.labels(endpoint='/download-csv', method='GET', status=500).inc()
        processed_documents_total.labels(doc_type='manual', status='500').inc()
        abort(500, description="Failed to send file.")
    finally:
        duration = time.time() - start_time
        api_request_duration.labels(endpoint='/download-csv', method='GET').observe(duration)

@app.route('/download-auto-csv/<task_id>', methods=['GET'])
def download_auto_csv(task_id):
    """
    Allows downloading the CSV file that has been automatically processing, identified by task_id.
    Removes empty columns (except essential ones) before download.
    Args:
        task_id (str): The unique identifier of the processing task.
    Returns:
        File download response or 404 error if file not found.
    """
    start_time = time.time()
    task = task_statuses.get(task_id)
    if not task or task.get('status') != 'completed':
        abort(404, description="Task not found or not completed.")

        csv_path = task.get('result')
        if not csv_path or not os.path.exists(csv_path):
            api_requests_total.labels(endpoint='/download-auto-csv', method='GET', status=404).inc()
            abort(404, description="CSV file not found.")

        if not os.path.abspath(csv_path).startswith(os.path.abspath(TEMP_CSV_DIR)):
            api_requests_total.labels(endpoint='/download-auto-csv', method='GET', status=400).inc()
            abort(400, description="Invalid file path.")

    try:
        app.logger.info(f"Serving file for download: {csv_path}")

        # Create a filtered version of the CSV
        base_name = os.path.splitext(os.path.basename(csv_path))[0]
        filtered_filename = f"{base_name}_filtered.csv"
        filtered_filepath = os.path.join(os.path.dirname(csv_path), filtered_filename)

        # Remove empty columns from the CSV
        remove_empty_columns_from_csv(csv_path, filtered_filepath)

        response = send_file(
            filtered_filepath,
            as_attachment=True,
            download_name=os.path.basename(csv_path),  # Keep original filename for download
            mimetype='text/csv'
        )

        # Schedule cleanup for both original and filtered files
        timer_original = threading.Timer(
            DOWNLOAD_CLEANUP_DELAY_SECONDS,
            lambda: with_app_context(delete_file_safely, csv_path)
        )
        timer_filtered = threading.Timer(
            DOWNLOAD_CLEANUP_DELAY_SECONDS,
            lambda: with_app_context(delete_file_safely, filtered_filepath)
        )
        timer_original.start()
        timer_filtered.start()
        app.logger.info(f"Scheduled {csv_path} and {filtered_filepath} for deletion in {DOWNLOAD_CLEANUP_DELAY_SECONDS} seconds.")

        return response

    except Exception as e:
        document_processing_errors_total.labels(error_type='auto_csv_download_error').inc()
        api_requests_total.labels(endpoint='/download-auto-csv', method='GET', status=500).inc()
        abort(500, description="Failed to send file.")
    finally:
        duration = time.time() - start_time
        api_request_duration.labels(endpoint='/download-auto-csv', method='GET').observe(duration)

#------------------ Auto Cleanup ------------------
# --- Scheduled Cleanup Task ---
def scheduled_inactive_files_cleanup():
    """
    Periodically scans TEMP_CSV_DIR and deletes files older than INACTIVITY_CLEANUP_THRESHOLD_HOURS.
    This runs within the Flask app context provided by APScheduler.
    """
    with app.app_context(): # Ensure app context for logging etc.
        app.logger.info("Running scheduled cleanup for inactive temporary CSV files...")
        now = time.time()
        threshold_seconds = INACTIVITY_CLEANUP_THRESHOLD_HOURS * 3600

        try:
            for filename in os.listdir(TEMP_CSV_DIR):
                if filename.endswith(".csv"): # Process only CSV files
                    filepath = os.path.join(TEMP_CSV_DIR, filename)
                    try:
                        file_mod_time = os.path.getmtime(filepath)
                        if (now - file_mod_time) > threshold_seconds:
                            app.logger.info(f"Found inactive file: {filepath}, age: {(now - file_mod_time)/3600:.2f} hours. Deleting.")
                            delete_file_safely(filepath)
                        else:
                            app.logger.debug(f"File {filepath} is active, age: {(now - file_mod_time)/3600:.2f} hours. Skipping.")
                    except FileNotFoundError:
                        # File might have been deleted by another process (e.g., post-download cleanup)
                        app.logger.info(f"File {filepath} not found during scheduled cleanup, likely already deleted.")
                    except Exception as e:
                        app.logger.error(f"Error processing file {filepath} during scheduled cleanup: {e}")
            for filename in os.listdir(TEMP_FILE_PROCESSING_DIR):
                if filename.endswith(".txt") or filename.endswith(".pdf"):
                    filepath = os.path.join(TEMP_FILE_PROCESSING_DIR, filename)
                    try:
                        file_mod_time = os.path.getmtime(filepath)
                        if (now - file_mod_time) > threshold_seconds:
                            app.logger.info(f"Found inactive file: {filepath}, age: {(now - file_mod_time)/3600:.2f} hours. Deleting.")
                            delete_file_safely(filepath)
                        else:
                            app.logger.debug(f"File {filepath} is active, age: {(now - file_mod_time)/3600:.2f} hours. Skipping.")
                    except FileNotFoundError:
                        # File might have been deleted by another process (e.g., post-download cleanup)
                        app.logger.info(f"File {filepath} not found during scheduled cleanup, likely already deleted.")
                    except Exception as e:
                        app.logger.error(f"Error processing file {filepath} during scheduled cleanup: {e}")
            app.logger.info("Scheduled cleanup finished.")
        except Exception as e:
            app.logger.error(f"General error during scheduled_inactive_files_cleanup: {e}")

scheduler = BackgroundScheduler(daemon=True)
scheduler.add_job(
    scheduled_inactive_files_cleanup,
    'interval',
    hours=CLEANUP_JOB_INTERVAL_HOURS,
    id='inactive_csv_cleanup_job', # Give the job an ID
    replace_existing=True # Replace if a job with this ID already exists (e.g. on app reload in debug)
)
# Start the scheduler only if it's not already running (e.g. in Flask's debug mode with reloader)
if not scheduler.running:
    scheduler.start()
    app.logger.info(f"APScheduler started. Cleanup job will run every {CLEANUP_JOB_INTERVAL_HOURS} hours.")
    # It's good practice to shut down the scheduler when the app exits
    
    atexit.register(lambda: scheduler.shutdown() if scheduler.running else None)

@app.route('/log-frontend-event', methods=['POST'])
def log_frontend_event():
    try:
        data = request.get_json(silent=True)
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        event_type = data.get('event_type', 'unknown_event')
        component = data.get('component', 'unknown_component')
        message = data.get('message', '')
        additional_details = data.get('details', {})

        log_message = f"[FRONTEND_EVENT] Type: {event_type}, Component: {component}, Message: {message}"
        if additional_details:
            log_message += f", Details: {additional_details}"

        app.logger.info(log_message)
        return jsonify({'status': 'success', 'message': 'Event logged'}), 200
    except Exception as e:
        app.logger.error(f"[FRONTEND_EVENT_ERROR] Error processing frontend event log: {str(e)}")
        return jsonify({'error': 'Failed to log event', 'details': str(e)}), 500

@app.route('/log-frontend-error', methods=['POST'])
def log_frontend_error():
    try:
        data = request.get_json(silent=True)
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        error_message = data.get('error_message', 'Unknown frontend error')
        component = data.get('component', 'unknown_component')
        details = data.get('details', {}) # Can include stack trace or other info

        log_message = f"[FRONTEND_ERROR] Message: {error_message}, Component: {component}"
        if details:
            log_message += f", Details: {details}"

        app.logger.error(log_message)
        return jsonify({'status': 'success', 'message': 'Error logged'}), 200
    except Exception as e:
        app.logger.error(f"[FRONTEND_ERROR_ERROR] Error processing frontend error log: {str(e)}")
        return jsonify({'error': 'Failed to log error', 'details': str(e)}), 500

if __name__ == '__main__':
    # Create temp directory if it doesn't exist
    os.makedirs(TEMP_FILE_PROCESSING_DIR, exist_ok=True)

    # Ensure the temporary directory exists
    os.makedirs(TEMP_CSV_DIR, exist_ok=True)

    # Run application based on environment
    if FLASK_ENV == 'production':
        # Production: This should not be reached when using Gunicorn
        # But keeping for fallback
        app.run(debug=False, host='0.0.0.0', port=6060)
    else:
        # Development configuration (commented for production)
        app.run(debug=True, host='0.0.0.0', port=6060)

