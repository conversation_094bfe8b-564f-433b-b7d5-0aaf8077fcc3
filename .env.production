# Production Environment Configuration

# Backend Configuration
FLASK_ENV=production
FLASK_APP=ProcessorAPI.py
FLASK_DEBUG=0
WORKERS=4

# Backend Volume Configuration (use named volumes for production)
BACKEND_VOLUME=backend_temp
BACKEND_VOLUME_CSVS=backend_temp_csvs
BACKEND_CODE_VOLUME=

# Frontend Configuration
FRONTEND_TARGET=production
FRONTEND_VOLUME=

# Docker Compose Project Name (optional)
COMPOSE_PROJECT_NAME=service_doc_processor_tdm
