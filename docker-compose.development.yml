# Development overrides for docker-compose.yml
# Use with: docker compose -f docker-compose.yml -f docker-compose.development.yml up

services:
  backend:
    volumes:
      - ./backend:/app
      - backend_temp_dev:/app/temp  # Use named volume for temp in development
      - backend_temp_csvs_dev:/app/temp_csvs  # Use named volume for temp_csvs in development
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - WORKERS=1

  frontend:
    build:
      target: development
    volumes:
      - ./frontend:/app
      - /app/node_modules  # Preserve node_modules from container
