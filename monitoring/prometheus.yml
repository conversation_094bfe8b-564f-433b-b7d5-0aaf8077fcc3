global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'document-processor-backend'
    static_configs:
      - targets: ['backend:6060']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    scheme: http
