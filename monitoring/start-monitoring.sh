#!/bin/bash

# Document Processor Monitoring Startup Script

set -e

echo "🚀 Starting Document Processor Monitoring Stack..."

# Check if docker compose is available
if ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker with Compose plugin."
    exit 1
fi

# Navigate to the project root
cd "$(dirname "$0")/.."

# Create necessary directories if they don't exist
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/grafana/provisioning/datasources
mkdir -p monitoring/grafana/provisioning/dashboards

echo "📊 Starting Prometheus and Grafana..."

# Start the monitoring stack
docker compose up -d prometheus grafana

echo "⏳ Waiting for services to be ready..."

# Wait for Prometheus
echo "Waiting for Prometheus..."
timeout=60
while ! curl -s http://localhost:9090/-/ready > /dev/null 2>&1; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "❌ Prometheus failed to start within 60 seconds"
        exit 1
    fi
done
echo "✅ Prometheus is ready"

# Wait for Grafana
echo "Waiting for Grafana..."
timeout=60
while ! curl -s http://localhost:3000/api/health > /dev/null 2>&1; do
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "❌ Grafana failed to start within 60 seconds"
        exit 1
    fi
done
echo "✅ Grafana is ready"

echo ""
echo "🎉 Monitoring stack is ready!"
echo ""
echo "📊 Access your monitoring tools:"
echo "   • Grafana Dashboard: http://localhost:3000"
echo "     - Username: admin"
echo "     - Password: admin (or your custom password)"
echo "   • Prometheus: http://localhost:9090"
echo "   • Simple Metrics Viewer: Open monitoring/simple-metrics-viewer.html in your browser"
echo ""
echo "🔧 Your application endpoints:"
echo "   • Backend API: http://localhost:6060"
echo "   • Frontend: http://localhost:400"
echo "   • Metrics endpoint: http://localhost:6060/metrics"
echo ""
echo "💡 Tips:"
echo "   • The 'Document Processor Metrics' dashboard is pre-configured in Grafana"
echo "   • Metrics are scraped every 10 seconds"
echo "   • Use 'docker compose logs prometheus' or 'docker compose logs grafana' to check logs"
echo ""
echo "🛑 To stop monitoring: docker compose down prometheus grafana"
