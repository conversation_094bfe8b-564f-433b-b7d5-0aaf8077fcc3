# Document Processor Monitoring

This directory contains monitoring and metrics visualization tools for the Document Processor application.

## Overview

The monitoring setup provides two options for viewing metrics:

1. **Full Prometheus + Grafana Stack** - Professional monitoring with rich dashboards
2. **Simple HTML Metrics Viewer** - Lightweight, standalone metrics viewer

## Quick Start

### Option 1: Full Monitoring Stack (Recommended)

1. **Start the services:**
   ```bash
   docker-compose up -d
   ```

2. **Access the interfaces:**
   - **Grafana Dashboard**: http://localhost:3000
     - Username: `admin`
     - Password: `admin` (or set via `GRAFANA_ADMIN_PASSWORD` env var)
   - **Prometheus**: http://localhost:9090
   - **Your App**: http://localhost:400 (frontend), http://localhost:6060 (backend)

3. **View the dashboard:**
   - The "Document Processor Metrics" dashboard will be automatically loaded
   - Shows real-time metrics including document processing rates, errors, and HTTP request metrics

### Option 2: Simple HTML Viewer

1. **Start your application:**
   ```bash
   docker-compose up backend frontend
   ```

2. **Open the metrics viewer:**
   - Open `monitoring/simple-metrics-viewer.html` in your web browser
   - The page will automatically fetch metrics from http://localhost:6060/metrics

## Available Metrics

Your application exposes the following custom metrics:

- `processed_documents_total` - Total documents processed (with status labels)
- `file_uploads_total` - Total files uploaded (with file type labels)
- `auto_process_tasks_total` - Total auto-processing tasks initiated
- `document_processing_errors_total` - Total processing errors

Plus standard Flask metrics:
- `flask_http_request_duration_seconds` - HTTP request latency
- `flask_http_request_total` - Total HTTP requests
- `flask_http_request_exceptions_total` - HTTP request exceptions

## Configuration

### Environment Variables

- `GRAFANA_ADMIN_PASSWORD` - Set Grafana admin password (default: admin)

### Customizing Dashboards

1. **Grafana Dashboards**: Edit files in `monitoring/grafana/dashboards/`
2. **Prometheus Config**: Edit `monitoring/prometheus.yml`
3. **Simple Viewer**: Edit `monitoring/simple-metrics-viewer.html`

## Troubleshooting

### Common Issues

1. **Metrics endpoint not accessible:**
   - Ensure your backend is running on port 6060
   - Check that prometheus-flask-exporter is properly configured

2. **Grafana shows "No data":**
   - Verify Prometheus is scraping metrics: http://localhost:9090/targets
   - Check that the backend service is reachable from the prometheus container

3. **Simple viewer shows CORS errors:**
   - Ensure your Flask app has CORS enabled for the metrics endpoint
   - Or serve the HTML file from the same domain as your backend

### Checking Metrics Manually

You can view raw metrics directly:
```bash
curl http://localhost:6060/metrics
```

## Advanced Usage

### Adding Custom Metrics

To add new metrics to your Flask app:

```python
# In ProcessorAPI.py
custom_metric = metrics.counter(
    'custom_metric_name',
    'Description of the metric',
    labels={'label_name': lambda: 'label_value'}
)

# Use in your endpoints
custom_metric.inc()
```

### Alerting

To set up alerting with Prometheus:

1. Create alert rules in `monitoring/prometheus.yml`
2. Configure Alertmanager (not included in this basic setup)
3. Set up notification channels (email, Slack, etc.)

## Files Structure

```
monitoring/
├── README.md                          # This file
├── prometheus.yml                     # Prometheus configuration
├── simple-metrics-viewer.html        # Standalone metrics viewer
└── grafana/
    ├── dashboards/
    │   └── document-processor-dashboard.json
    └── provisioning/
        ├── dashboards/
        │   └── dashboard.yml
        └── datasources/
            └── prometheus.yml
```
