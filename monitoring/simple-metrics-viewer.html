<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Processor Metrics</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .metric-description {
            font-size: 12px;
            color: #888;
        }
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .raw-metrics {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .raw-metrics pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
        .auto-refresh {
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Document Processor Metrics Dashboard</h1>
            <p>Real-time monitoring of your document processing service</p>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <div>
            <button class="refresh-btn" onclick="fetchMetrics()">Refresh Metrics</button>
            <label class="auto-refresh">
                <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()"> Auto-refresh (30s)
            </label>
        </div>

        <div class="metrics-grid" id="metricsGrid">
            <!-- Metrics will be populated here -->
        </div>

        <div class="raw-metrics">
            <h3>Raw Metrics Data</h3>
            <pre id="rawMetrics">Loading...</pre>
        </div>
    </div>

    <script>
        let autoRefreshInterval;
        const BACKEND_URL = 'http://localhost:6060'; // Adjust if needed

        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${isError ? 'error' : 'success'}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function parsePrometheusMetrics(text) {
            const lines = text.split('\n');
            const metrics = {};
            
            for (const line of lines) {
                if (line.startsWith('#') || line.trim() === '') continue;
                
                const match = line.match(/^([a-zA-Z_:][a-zA-Z0-9_:]*(?:\{[^}]*\})?) (.+)$/);
                if (match) {
                    const [, metricName, value] = match;
                    const baseName = metricName.split('{')[0];
                    
                    if (!metrics[baseName]) {
                        metrics[baseName] = [];
                    }
                    metrics[baseName].push({
                        name: metricName,
                        value: parseFloat(value)
                    });
                }
            }
            
            return metrics;
        }

        function displayMetrics(metrics) {
            const grid = document.getElementById('metricsGrid');
            grid.innerHTML = '';

            // Key metrics to highlight
            const keyMetrics = [
                // Document Processing Metrics
                {
                    key: 'processed_documents_total',
                    title: 'Documents Processed',
                    description: 'Total number of documents processed by type and status'
                },
                {
                    key: 'document_processing_duration_seconds',
                    title: 'Processing Duration',
                    description: 'Average time taken for document processing operations'
                },
                {
                    key: 'document_processing_errors_total',
                    title: 'Processing Errors',
                    description: 'Total errors during document processing by type'
                },
                
                // API Performance Metrics
                {
                    key: 'api_request_duration_seconds',
                    title: 'API Response Time',
                    description: 'Average response time for API requests'
                },
                {
                    key: 'api_requests_total',
                    title: 'API Requests',
                    description: 'Total number of API requests by endpoint and status'
                },
                
                // File Operation Metrics
                {
                    key: 'file_uploads_total',
                    title: 'Files Uploaded',
                    description: 'Total number of files uploaded by type'
                },
                {
                    key: 'csv_operations_total',
                    title: 'CSV Operations',
                    description: 'Total number of CSV operations by type'
                },
                
                // Task Management Metrics
                {
                    key: 'auto_process_tasks_total',
                    title: 'Auto Process Tasks',
                    description: 'Total auto-processing tasks initiated'
                },
                {
                    key: 'active_processing_tasks',
                    title: 'Active Tasks',
                    description: 'Number of currently active processing tasks'
                }
            ];

            keyMetrics.forEach(metric => {
                const metricData = metrics[metric.key];
                let value = 0;
                let formattedValue = '0';
                
                if (metricData) {
                    if (metric.key.includes('duration')) {
                        // For duration metrics, calculate average
                        const sum = metricData.reduce((sum, item) => sum + item.value, 0);
                        value = sum / metricData.length;
                        formattedValue = `${value.toFixed(2)}s`;
                    } else if (metric.key === 'active_processing_tasks') {
                        // For gauge metrics, show the current value
                        value = metricData[0]?.value || 0;
                        formattedValue = value.toString();
                    } else {
                        // For counter metrics, sum all values
                        value = metricData.reduce((sum, item) => sum + item.value, 0);
                        formattedValue = value.toLocaleString();
                    }
                }

                const card = document.createElement('div');
                card.className = 'metric-card';
                card.innerHTML = `
                    <div class="metric-title">${metric.title}</div>
                    <div class="metric-value">${formattedValue}</div>
                    <div class="metric-description">${metric.description}</div>
                `;
                grid.appendChild(card);
            });
        }

        async function fetchMetrics() {
            try {
                showStatus('Fetching metrics...');
                console.log('Fetching metrics...')
                const response = await fetch(`${BACKEND_URL}/metrics`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const text = await response.text();
                document.getElementById('rawMetrics').textContent = text;
                
                const metrics = parsePrometheusMetrics(text);
                console.log(metrics)
                displayMetrics(metrics);
                
                showStatus('Metrics updated successfully');
            } catch (error) {
                console.error('Error fetching metrics:', error);
                showStatus(`Error: ${error.message}`, true);
                document.getElementById('rawMetrics').textContent = `Error: ${error.message}`;
            }
        }

        function toggleAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            if (checkbox.checked) {
                autoRefreshInterval = setInterval(fetchMetrics, 30000);
                showStatus('Auto-refresh enabled (30s interval)');
            } else {
                clearInterval(autoRefreshInterval);
                showStatus('Auto-refresh disabled');
            }
        }

        // Initial load
        fetchMetrics();
    </script>
</body>
</html>
